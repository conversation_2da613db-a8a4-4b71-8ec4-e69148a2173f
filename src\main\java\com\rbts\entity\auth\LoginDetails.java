package com.rbts.entity.auth;

import com.rbts.encryption.Encrypt;
import com.rbts.encryption.EncryptionEntityListener;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name="login_details")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(EncryptionEntityListener.class)
public class LoginDetails  extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "loginSequenceGenerator")
    @SequenceGenerator(name = "loginSequenceGenerator",allocationSize = 1, initialValue = 1)
    @Column(name = "id")
    private Long id;


    @Encrypt
    @Column(name="username",nullable = false)
    private String username;

    @Column(name="login_time",nullable = false, updatable = false)
    private LocalDateTime loginTime;

    @Column(name="logout_time")
    private LocalDateTime logoutTime;

    @Column(name="active",nullable = false)
    private boolean active;

    @Column(name="token",nullable = false,columnDefinition = "TEXT")
    private String token;

    @Column(name="lat_details")
    private String latDetails;

    @Column(name="long_details")
    private String longDetails;

    @Column(name="ip_address",length = 10)
    private String ipAddress;

}
