package com.rbts.security;

import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.auth.LoginDetails;
import com.rbts.exception.AppProperties;
import com.rbts.exception.UnAuthorizedException;
import com.rbts.repository.LoginDetailsRepository;
import com.rbts.service.auth.TokenBlacklistService;
import com.rbts.service.auth.UsersService;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${app.jwtSecret}")
    private String jwtSecret ;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Value("${app.jwtExpirationMs}")
    private int jwtExpirationMs;

    @Autowired
    LoginDetailsRepository loginDetailsRepository;

    @Autowired
    TokenBlacklistService tokenBlacklistService;

    @Autowired
    AppProperties appProperties;


    public String getUserNameFromJwtToken(String token) {
        Key key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        Claims claims = Jwts.parser()
                .setSigningKey(key)
                .parseClaimsJws(token)
                .getBody();
        return claims.getSubject();
    }


    public String generateJwtToken(UsersService userPrincipal, Set<String> permissions) {
        return generateTokenFromUsername(userPrincipal.getUsername(),permissions);
    }

    public String generateTokenFromUsername(String username, Set<String> permissionsSet) {
        List<String> permissionKeys = new ArrayList<>();

        try {
            permissionKeys = permissionsSet.stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // Optionally log the error
            permissionKeys = new ArrayList<>();
        }

        Key key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .claim("permissions", permissionKeys)
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }

    public boolean validateJwtToken(String authToken) throws Exception {
        try {
            Key key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
            Jwts.parser().setSigningKey(key).parseClaimsJws(authToken);
            tokenBlacklistService.isTokenBlacklisted(authToken);
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
            throw new UnAuthorizedException(appProperties.getInvalidToken());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
            throw new UnAuthorizedException(appProperties.getInvalidToken());
        } catch (ExpiredJwtException e) {
            String username = e.getClaims().getSubject();
            List<LoginDetails> loginDetails = loginDetailsRepository.findByUsernameAndActiveIsTrue(base64EncryptionUtils.encrypt(username));
            for (LoginDetails loginDetails1:loginDetails) {
                LoginDetails loginDetails2 =loginDetailsRepository.findByUsername2();
                loginDetails2.setLogoutTime(LocalDateTime.now());
                loginDetails2.setActive(false);
                loginDetailsRepository.save(loginDetails2);
            }
            logger.error("JWT token is expired: {}", e.getMessage());
            throw new UnAuthorizedException(appProperties.getInvalidToken());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
            throw new UnAuthorizedException(appProperties.getInvalidToken());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
            throw new UnAuthorizedException(appProperties.getInvalidToken());
        }
        try {
            return !tokenBlacklistService.isTokenBlacklisted(authToken);
        }catch (Exception e){
            throw new Exception(e);
        }
    }


}
