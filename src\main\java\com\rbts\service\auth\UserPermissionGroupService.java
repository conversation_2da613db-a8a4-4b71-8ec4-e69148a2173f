package com.rbts.service.auth;

import com.rbts.entity.auth.ClientCustomGroup;
import com.rbts.entity.auth.UserPermissionGroup;
import com.rbts.entity.auth.Users;
import com.rbts.repository.ClientCustomGroupRepository;
import com.rbts.repository.UserPermissionGroupRepository;
import com.rbts.repository.UsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserPermissionGroupService {

    private final UserPermissionGroupRepository repository;
    private final UsersRepository usersRepository;
    private final ClientCustomGroupRepository clientCustomGroupRepository;

    public UserPermissionGroup create(UserPermissionGroup userPermissionGroup) {
        Users user = usersRepository.findById(userPermissionGroup.getUserId().getId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userPermissionGroup.getUserId().getId()));

        ClientCustomGroup customGroup = clientCustomGroupRepository.findById(userPermissionGroup.getCustomGroupKey().getId())
                .orElseThrow(() -> new RuntimeException("ClientCustomGroup not found with ID: " + userPermissionGroup.getCustomGroupKey().getId()));

        userPermissionGroup.setUserId(user);
        userPermissionGroup.setCustomGroupKey(customGroup);
        return repository.save(userPermissionGroup);
    }

    public UserPermissionGroup update(Long id, UserPermissionGroup updated) {
        UserPermissionGroup existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("UserPermissionGroup not found with ID: " + id));

        Users user = usersRepository.findById(updated.getUserId().getId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + updated.getUserId().getId()));

        ClientCustomGroup customGroup = clientCustomGroupRepository.findById(updated.getCustomGroupKey().getId())
                .orElseThrow(() -> new RuntimeException("ClientCustomGroup not found with ID: " + updated.getCustomGroupKey().getId()));

        existing.setUserId(user);
        existing.setCustomGroupKey(customGroup);
        existing.setIsActive(updated.getIsActive());

        return repository.save(existing);
    }

    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new RuntimeException("UserPermissionGroup not found with ID: " + id);
        }
        repository.deleteById(id);
    }

    public Optional<UserPermissionGroup> getById(Long id) {
        return repository.findById(id);
    }

    public List<UserPermissionGroup> getByUserId(Long userId) {
        return repository.findByUserId_Id(userId);
    }

    public List<UserPermissionGroup> getAll() {
        return repository.findAll();
    }
}
