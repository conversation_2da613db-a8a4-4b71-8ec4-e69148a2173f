package com.rbts.entity;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.io.Serializable;
import java.util.List;

/**
 * A MasterManager.
 */
@Entity
@Table(name = "master_manager")
@SuppressWarnings("common-java:DuplicatedBlocks")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MasterManager implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "master_name", nullable = false)
    private String masterName;

    @NotNull
    @Column(name = "display_name", nullable = false)
    private String displayName;

    @Column(name = "master_desc")
    private String masterDesc;

    @NotNull
    @Column(name = "get_api_url", nullable = false)
    private String getApiUrl;

    @NotNull
    @Column(name = "save_api_url", nullable = false)
    private String saveApiUrl;

    @NotNull
    @Column(name = "update_api_url", nullable = false)
    private String updateApiUrl;

    @NotNull
    @Column(name = "display_preference", nullable = false)
    private Long displayPreference;

    @NotNull
    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible;

    @OneToMany(mappedBy = "masterManager", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MasterConfig> masterConfigs;

}
