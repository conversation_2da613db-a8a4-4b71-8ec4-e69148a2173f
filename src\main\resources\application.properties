

spring.datasource.url=jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driveName=org.postgresql.Driver
spring.datasource.data=classpath:Auth.sql

# Hikari-specific configuration
spring.datasource.hikari.maximum-pool-size=50000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.pool-name=MyHikariPool
spring.datasource.hikari.connection-timeout=300000
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.max-lifetime=1800000
server.servlet.session.timeout=5m

#--------------------JPA-ORM Properties-----------------
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
server.port=9766
spring.application.name=locate-tutor-service
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
#-------------------------------------------
spring.redis.host=${DB_HOST:localhost}
#spring.redis.host=redis-service
spring.redis.port=6379
spring.redis.timeout=60000

#----------------------------------------------------------------
app.jwtSecret= 7a9128f8c1df56625ae4bb300d254cbcdb29bace578b59e2623e25e7917c442218b35e0ad8657af57e8f7955eab10c9095dfd404e601b1dfa7a812707997ba510dfa3db9a960c48a0ab2b9f4437fa949c9c75e0daf8c5f61c1411faf62c809e730b7799eb1b296a8c8e59f8e79c4f5f163e6073387595d1d11343553314f72f7e2d9c16f7d2d80ab199a977638b0edf2de0ce73199f808f2138e2240f0c6374638ff98e194bb41fe65589d9e0894ddf872634101d08999e887d3d160b383e244b7bee4e60446eed14634506713a04d366aea88f942845a56d2493682949986482afb5dc432ef664bad8346cad8ba9c44039ae246036a23ead8313ab7856e031296544906b2c4c3b148cfea248d07f371f570648bbb46549f6622eb32427936f716b4170663b068c3ed6dfc46a8e6b1972d037d8448a1f0e85ffc64d6b05c0085
app.jwtExpirationMs= 43200000
app.jwtRefreshExpirationMs= 86400000
ribbon.ReadTimeout=86400000

spring.main.allow-circular-references=true
spring.main.allow-bean-definition-overriding=true

authentication.nullData=Passing Null Value
authentication.duplicateData=Passing duplicate Value
authentication.currentPassword=Incorrect current password
authentication.contactNo=ContactNo is already present
authentication.newPassword=New password has been used recently
authentication.employeeIdPresent=Employee ID already present
authentication.empId = Employee Id cannot be null
authentication.contactDetails = Contact No cannot be null
authentication.employeeId1 = Incorrect EmployeeId
authentication.user = Username cannot be null
authentication.usernameAlreadyTaken = Username is already taken!
authentication.data = Data not Found
authentication.pass = Password cannot be null
authentication.invalid = Invalid Credential
authentication.role=Role is not found
authentication.role1=Role cannot be null
authentication.roleAlreadyExist= Role Already Exist.
authentication.statusNotFound=Status not found!
authentication.admin=Admin role already present
authentication.users=User details not found
authentication.currentPassword1 = Current Password cannot be null
authentication.newPassword1 = New Password cannot be null
authentication.host=mail.smtp.host
authentication.host1=smtp.gmail.com
authentication.port=mail.smtp.port
authentication.port1=465
authentication.ssl=mail.smtp.ssl.enable
authentication.ssl1=true
authentication.auth=mail.smtp.auth
authentication.auth1=true
authentication.password1=Password Changed successfully!
authentication.subject=OTP From RedBerylTech;
authentication.otp=OTP=
authentication.otpMsg=We have send OTP to your email
authentication.otpMsg1=Issue in sending OTP
authentication.otpMsg2=OTP match
authentication.otpMsg3=You have entered wrong OTP !!
authentication.otpMsg4=Session has expired. Please request a new OTP !
authentication.value=Passing null value
authentication.feignClient=Transactional error Due to Feign Client
authentication.usernameNotFound=Username Not Found
authentication.superAdminUser=Username is not a Super Admin
authentication.companyNotFound=CompanyId not found in userRoleDetails.
authentication.secretKey=VStateFilingsJwt
authentication.padding=AES/ECB/PKCS5Padding
authentication.authorization=Authorization
authentication.feignIds="statusId", "rolesId"
authentication.unknownId =Unknown id: 
authentication.invalidId = Given id does not exist : 
authentication.unAuthorized = Error: Unauthorized
authentication.invalidToken = Invalid JWT token
authentication.loggedOut = User already logged out!
authentication.loggedOutSuccess = User has been logged out successfully!
authentication.userRegisteredSuccess = User registered successfully!
authentication.statusUpdated = Status Updated Successfully!
authentication.userId = 3
authentication.employeeId = 4
authentication.vendorId = 4
authentication.accountBlocked = Your Account Has Been Blocked By Admin Please Contact Your Administrator.



# Microsoft OAuth (Azure AD)
spring.security.oauth2.client.registration.microsoft.client-id=9898f5b8-aba7-4b24-9a1f-a0d8152fd377
spring.security.oauth2.client.registration.microsoft.client-secret=****************************************
spring.security.oauth2.client.registration.microsoft.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.microsoft.redirect-uri=http://localhost:9766/login/oauth2/code/microsoft
spring.security.oauth2.client.registration.microsoft.scope=openid,profile,email,User.Read
spring.security.oauth2.client.provider.microsoft.jwk-set-uri=https://login.microsoftonline.com/common/discovery/v2.0/keys
spring.security.oauth2.client.provider.microsoft.authorization-uri=https://login.microsoftonline.com/common/oauth2/v2.0/authorize
spring.security.oauth2.client.provider.microsoft.token-uri=https://login.microsoftonline.com/common/oauth2/v2.0/token
spring.security.oauth2.client.provider.microsoft.user-info-uri=https://graph.microsoft.com/oidc/userinfo
spring.security.oauth2.client.provider.microsoft.user-name-attribute=sub


# GOOGLE OAUTH CONFIG
spring.security.oauth2.client.registration.google.client-id=603411267502-njqm22q76vqed2k10hg922hc01c4vv05.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:9766/login/oauth2/code/google
spring.security.oauth2.client.registration.google.authorization-grant-type=authorization_code
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/v2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://openidconnect.googleapis.com/v1/userinfo
spring.security.oauth2.client.provider.google.user-name-attribute=sub



spring.security.oauth2.client.registration.linkedin.client-id=868060fnerbf2n
spring.security.oauth2.client.registration.linkedin.client-secret=WPL_AP1.J0KDsIhrqIplMogX.fMjqqA==
spring.security.oauth2.client.registration.linkedin.scope=r_liteprofile,r_emailaddress
spring.security.oauth2.client.registration.linkedin.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.linkedin.redirect-uri=http://localhost:9766/login/oauth2/code/linkedin
spring.security.oauth2.client.provider.linkedin.authorization-uri=https://www.linkedin.com/oauth/v2/authorization
spring.security.oauth2.client.provider.linkedin.token-uri=https://www.linkedin.com/oauth/v2/accessToken
spring.security.oauth2.client.provider.linkedin.user-info-uri=https://api.linkedin.com/v2/me?projection=(id,localizedFirstName,localizedLastName,profilePicture(displayImage~:playableStreams))
spring.security.oauth2.client.provider.linkedin.user-name-attribute=id


spring.security.oauth2.client.registration.facebook.client-id=YOUR_FACEBOOK_APP_ID
spring.security.oauth2.client.registration.facebook.client-secret=YOUR_FACEBOOK_APP_SECRET
spring.security.oauth2.client.registration.facebook.scope=email,public_profile
spring.security.oauth2.client.registration.facebook.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.facebook.redirect-uri=http://localhost:9766/login/oauth2/code/facebook

spring.security.oauth2.client.provider.facebook.authorization-uri=https://www.facebook.com/v13.0/dialog/oauth
spring.security.oauth2.client.provider.facebook.token-uri=https://graph.facebook.com/v13.0/oauth/access_token
spring.security.oauth2.client.provider.facebook.user-info-uri=https://graph.facebook.com/v13.0/me?fields=id,name,first_name,last_name,email,picture
spring.security.oauth2.client.provider.facebook.user-name-attribute=id



# Apple OAuth2 setup
spring.security.oauth2.client.registration.apple.client-id=com.example.app
spring.security.oauth2.client.registration.apple.client-secret=${APPLE_CLIENT_SECRET_JWT}
spring.security.oauth2.client.registration.apple.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.apple.redirect-uri=http://localhost:9766/login/oauth2/code/apple
spring.security.oauth2.client.registration.apple.scope=openid,name,email

# Calendly API Configuration
calendly.api.url=https://api.calendly.com
calendly.organization-uuid=${CALENDLY_ORGANIZATION_UUID:your-organization-uuid}
# Note: Add calendly.api.token to system_config table with key 'calendly.api.token'

spring.security.oauth2.client.provider.apple.authorization-uri=https://appleid.apple.com/auth/authorize?response_mode=form_post&response_type=code+id_token
spring.security.oauth2.client.provider.apple.token-uri=https://appleid.apple.com/auth/token
spring.security.oauth2.client.provider.apple.jwk-set-uri=https://appleid.apple.com/auth/keys
spring.security.oauth2.client.provider.apple.user-name-attribute=sub
