package com.rbts.controller.common;

import com.rbts.dto.QueryRequestDto;
import com.rbts.exception.FeignClientException;
import com.rbts.service.common.DynamicEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class DynamicEntityController {

    private final DynamicEntityService dynamicEntityService;

    @PostMapping("/{entityName}/save")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> saveEntity(
            @PathVariable String entityName,
            @RequestBody Map<String, Object> entityData) {
        log.info("Saving entity: {} with data: {}", entityName, entityData);
        return ResponseEntity.status(201).body(dynamicEntityService.saveEntity(entityName, entityData));
    }


    @GetMapping("/{entityName}/list")
//    @PreAuthorize("hasPermission(#entityName, 'READ')")
    @PreAuthorize("permitAll()")
    public ResponseEntity<List<Map<String, Object>>> listEntities(
            @PathVariable String entityName) {
        log.info("Fetching all entities of type: {}", entityName);
        return ResponseEntity.ok(dynamicEntityService.listEntities(entityName));
    }

    @PutMapping("/{entityName}/update/{id}")
//    @PreAuthorize("hasPermission(#entityName, 'UPDATE')")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> updateEntity(
            @PathVariable String entityName,
            @PathVariable Long id,
            @RequestBody Map<String, Object> entityData) {
        log.info("Updating entity: {} with ID: {} and data: {}", entityName, id, entityData);
        return ResponseEntity.ok(dynamicEntityService.updateEntity(entityName, id, entityData));
    }

    @DeleteMapping("/{entityName}/delete/{id}")
//    @PreAuthorize("hasPermission(#entityName, 'DELETE')")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Void> deleteEntity(
            @PathVariable String entityName,
            @PathVariable Long id) {
        log.info("Deleting entity: {} with ID: {}", entityName, id);
        dynamicEntityService.deleteEntity(entityName, id);
        return ResponseEntity.noContent().build();
    }


    @GetMapping("/{entityName}/{id}")
//    @PreAuthorize("hasPermission(#entityName, 'READ')")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> listEntitiesById(
            @PathVariable String entityName, @PathVariable Long id) {
        log.info("Fetching entities by Id of type: {}", entityName);
        return ResponseEntity.ok(dynamicEntityService.getEntityById(entityName, id));
    }


    @PostMapping("/getEntitiesByDynamicQuery")
    @PreAuthorize("permitAll()")
    public ResponseEntity<List<Map<String, Object>>> getEntitiesByDynamicQuery(
            @RequestBody QueryRequestDto queryRequestDto) {
        log.info("Executing query: {}", queryRequestDto.getQueryId());
        List<Map<String, Object>> results = dynamicEntityService.executeNativeQuery(queryRequestDto);
        return ResponseEntity.ok(results);
    }


    @PatchMapping("/updatePartialEntity/{entityName}/{id}")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> UpdatePartial(
            @PathVariable String entityName,
            @PathVariable Long id,
            @RequestBody Map<String, Object> updates) {
        log.info("Partial Updating entity: {} with ID: {} with data: {}", entityName, id, updates);
        Map<String, Object> updatedEntity = dynamicEntityService.partialUpdateEntity(entityName, id, updates);
        return ResponseEntity.ok(updatedEntity);
    }

    @GetMapping("/{entityName}/filter/{fieldName}/{fieldValue}")
    @PreAuthorize("permitAll()")
    public ResponseEntity<List<Map<String, Object>>> listEntitiesByField(
            @PathVariable String entityName,
            @PathVariable String fieldName,
            @PathVariable String fieldValue) {
        log.info("Fetching entities of type {} where {} = {}", entityName, fieldName, fieldValue);
        return ResponseEntity.ok(dynamicEntityService.getEntitiesByField(entityName, fieldName, fieldValue));
    }

    @GetMapping("/{entityName}/sort/{sortBy}/{sortOrder}")
    @PreAuthorize("permitAll()")
    public ResponseEntity<List<Map<String, Object>>> listEntitiesSorted(
            @PathVariable String entityName,
            @PathVariable String sortBy,
            @PathVariable String sortOrder) {
        log.info("Fetching and sorting entities of type {} by {} in {} order", entityName, sortBy, sortOrder);
        return ResponseEntity.ok(dynamicEntityService.getEntitiesSorted(entityName, sortBy, sortOrder));
    }

    @PostMapping("/{entityName}/bulkSave")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> saveMultipleEntity(
            @PathVariable String entityName,
            @RequestBody Map<String, Object> entityData) throws FeignClientException {
        log.info("Saving entity: {} with data: {}", entityName, entityData);
        return ResponseEntity.status(201).body(dynamicEntityService.saveBulkEntity(entityName, entityData));
    }

//    @GetMapping("/{entityName}/getById/{id}")
////    @PreAuthorize("hasPermission(#entityName, 'READ')")
//    @PreAuthorize("permitAll()")
//    public ResponseEntity<Map<String, Object>> listEntitiesByIdWithFeignData(
//            @PathVariable String entityName, @PathVariable Long id) {
//        log.info("Fetching entities by Id of type: {}", entityName);
//        return ResponseEntity.ok(dynamicEntityService.listEntitiesByIdWithFeignData(entityName, id));
//    }
}