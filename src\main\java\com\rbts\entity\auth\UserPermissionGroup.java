package com.rbts.entity.auth;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_permission_groups")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class  UserPermissionGroup extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userpermissiongroupsequencegenerator")
    @GenericGenerator(
            name = "userpermissiongroupsequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "userpermissiongroupsequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private Users userId;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "custom_group_key",referencedColumnName = "group_key" ,nullable = false)
    private ClientCustomGroup customGroupKey;


    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
    
}
