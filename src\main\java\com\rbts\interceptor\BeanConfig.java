package com.rbts.interceptor;

import com.rbts.encryption.EncryptionEntityListener;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import jakarta.servlet.http.HttpServletRequest;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "aware")
public class BeanConfig {


    @Value("${app.jwtSecret}")
    private String jwtSecret ;

    @Bean
    public AuditorAware<String> aware(HttpServletRequest request) {
        return () -> {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7); // Extract the token
                System.out.println("Token extracted: " + token);

                // Handle the case where the token might be null or empty
                if (token == null || token.isEmpty()) {
                    System.out.println("JWT token is null or empty.");
                    return Optional.empty(); // Return empty if no valid token is found
                }

                // Validate that the token contains 2 periods (to avoid MalformedJwtException)
                if (token.chars().filter(ch -> ch == '.').count() != 2) {
                    System.out.println("Invalid JWT format.");
                    return Optional.empty(); // Return empty if the token is malformed
                }

                try {
                    String username = getUsernameFromToken(token); // Extract username from token
                    new EncryptionEntityListener().decrypt(username);
                    System.out.println("Decrypted username: " + username);
                    return Optional.ofNullable(username);
                } catch (MalformedJwtException e) {
                    System.out.println("Error parsing JWT: " + e.getMessage());
                    return Optional.empty(); // Return empty if token parsing fails
                }
            }
            return Optional.empty(); // Return empty if Authorization header is not present or invalid
        };
    }

    private String getUsernameFromToken(String token) {
        Key key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        Claims claims = Jwts.parser()
                .setSigningKey(key)
                .parseClaimsJws(token)
                .getBody();
        return claims.getSubject();
    }

}
