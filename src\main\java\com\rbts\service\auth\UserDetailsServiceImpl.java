package com.rbts.service.auth;

import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.entity.auth.Users;
import com.rbts.exception.AppProperties;
import com.rbts.exception.ResourceNotFoundException;
import com.rbts.repository.UserRoleDetailsRepository;
import com.rbts.repository.UsersRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class UserDetailsServiceImpl implements UserDetailsService {


    @Autowired
    UsersRepository usersRepository;

    @Autowired
    AppProperties appProperties;

    @Autowired
    UserRoleDetailsRepository userRoleDetailsRepository;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;



    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) {
        Users user = null;
        try {
            user = usersRepository.findByUsername(username);
            System.out.println("&&&&"+user);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<UserRolesDetails> userRolesDetails = userRoleDetailsRepository.findByUserId(user.getId());
                if(user!=null)
                {
                    return UsersService.build(user,userRolesDetails);
                }else {
                    try {
                        throw new ResourceNotFoundException(appProperties.getInvalid());
                    } catch (ResourceNotFoundException e) {
                        throw new RuntimeException(e);
                    }
                }
    }
}
