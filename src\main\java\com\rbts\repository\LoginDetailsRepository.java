package com.rbts.repository;

import com.rbts.entity.auth.LoginDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface LoginDetailsRepository extends JpaRepository<LoginDetails, Long> {

    List<LoginDetails> findByUsernameAndActiveIsTrue(String username);

    @Query("SELECT l FROM LoginDetails l WHERE l.username = :username ORDER BY l.loginTime DESC")
    List<LoginDetails> findByUsername(@Param("username") String username);

    @Query("SELECT l FROM LoginDetails l WHERE l.username = :username")
    List<LoginDetails> findByUsername1(@Param("username") String username);

    @Query("SELECT l FROM LoginDetails l WHERE l.active = true")
    LoginDetails findByUsername2();

    // ✅ Already valid
    @Query("SELECT l FROM LoginDetails l WHERE l.active = true AND l.username = :username")
    LoginDetails findByUsername3(@Param("username") String username);

    boolean existsByTokenAndLogoutTimeIsNotNull(String token);

    LoginDetails findByToken(String token);

    @Query("SELECT COUNT(ld) FROM LoginDetails ld WHERE ld.username = :username")
    Long countByUsername(@Param("username") String username);
}
