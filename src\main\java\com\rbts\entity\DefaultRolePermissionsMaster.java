package com.rbts.entity;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "default_role_permissions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DefaultRolePermissionsMaster {


    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "defaultrolepermissionquencegenerator")
    @GenericGenerator(
            name = "defaultrolepermissionquencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "defaultrolepermissionquencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "role_key", referencedColumnName = "role_key", nullable = false)
    private RoleMaster roleKey;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "permission_key", referencedColumnName = "permission_key", nullable = false)
    private PermissionMaster permissionKey;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

}
