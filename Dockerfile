# Stage 1: Use the OpenJDK image as base
FROM openjdk:17

# Set the working directory inside the container
WORKDIR /app

# Copy the JAR file from the host machine into the container
COPY target/authentication-service.jar  app/authentication-service.jar

# Expose the port that the application runs on
EXPOSE 7000

# Set environment variables for PostgreSQL connection
ENV SPRING_DATASOURCE_URL=jdbc:postgresql://*************/centralDB
ENV SPRING_DATASOURCE_USERNAME=postgres
ENV SPRING_DATASOURCE_password=postgres
ENV SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.postgresql.Driver

# Command to run the application
CMD ["java", "-jar", "app/authentication-service.jar"]

