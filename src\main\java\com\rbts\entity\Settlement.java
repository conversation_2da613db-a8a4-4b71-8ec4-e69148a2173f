package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "settlements")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Settlement {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "settlementSeqGen")
    @SequenceGenerator(name = "settlementSeqGen", sequenceName = "settlements_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutorId;

    @NotNull
    @Column(name = "cycle_start", nullable = false)
    private LocalDate cycleStart;

    @NotNull
    @Column(name = "cycle_end", nullable = false)
    private LocalDate cycleEnd;

    @NotNull
    @Column(name = "total_earnings", nullable = false)
    private BigDecimal totalEarnings;

    @NotNull
    @Column(name = "platform_fee", nullable = false)
    private BigDecimal platformFee;

    @NotNull
    @Column(name = "net_amount", nullable = false)
    private BigDecimal netAmount;

    @Column(name = "payment_status")
    private String paymentStatus;

    @Column(name = "initiated_at")
    private LocalDateTime initiatedAt;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;
}
