package com.rbts.service.auth;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

@Service
public class OtpRedisService {

    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public OtpRedisService(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void storeOtp(String email, String otp, long timeoutMinutes) {
        redisTemplate.opsForValue().set(email, otp, Duration.ofMinutes(timeoutMinutes));
    }

    public String getOtp(String email) {
        return redisTemplate.opsForValue().get(email);
    }

    public void removeOtp(String email) {
        redisTemplate.delete(email);
    }
}
