//package com.rbts.entity;
//
//
//import com.rbts.encryption.Encrypt;
//import com.rbts.encryption.EncryptionEntityListener;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import jakarta.persistence.*;
//import jakarta.validation.constraints.NotNull;
//import java.time.LocalDateTime;
//
//@Entity
//@Table(name = "temp")
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Builder
//@EntityListeners(EncryptionEntityListener.class)
//public class Temp {
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "temp_sequence_generator")
//    @SequenceGenerator(name = "temp_sequence_generator",allocationSize = 1, initialValue = 1)
//    @Column(name = "id")
//    private Long id;
//
//
//    @Encrypt
//    @NotNull
//    @Column(nullable = false)
//    private String username;
//
//    @NotNull
//    @Column(nullable = false)
//    private int otp;
//
//    @Column(nullable = false)
//    private LocalDateTime createdAt;
//}
