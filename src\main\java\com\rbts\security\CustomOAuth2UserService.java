package com.rbts.security;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomOAuth2UserService extends DefaultOAuth2UserService {

    private final HttpServletRequest request;

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) {
        Object stateObj = userRequest.getAdditionalParameters().get("state");
        if (stateObj != null) {
            String state = stateObj.toString();
            if (state.contains("::role=")) {
                String role = state.split("::role=")[1];
                HttpSession session = request.getSession(true);
                session.setAttribute("oauth2_role", role);
                log.info("✅ Stored role '{}' in session", role);
            } else {
                log.warn("⚠️ State found but no role: {}", state);
            }
        } else {
            log.warn("⚠️ No state found in OAuth2UserRequest");
        }

        return super.loadUser(userRequest);
    }
}
