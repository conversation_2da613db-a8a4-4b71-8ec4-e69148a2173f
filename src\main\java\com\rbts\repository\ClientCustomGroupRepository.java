package com.rbts.repository;

import com.rbts.entity.auth.ClientCustomGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ClientCustomGroupRepository extends JpaRepository<ClientCustomGroup, Long> {
    boolean existsByGroupKey(String groupKey);
    Optional<ClientCustomGroup> findByGroupKey(String groupKey);

}

