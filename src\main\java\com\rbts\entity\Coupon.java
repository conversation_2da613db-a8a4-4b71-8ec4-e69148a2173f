package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "coupons")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Coupon {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "couponSeqGen")
    @SequenceGenerator(name = "couponSeqGen", sequenceName = "coupons_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "coupon_code", nullable = false, unique = true)
    private String couponCode;

    @NotNull
    @Column(name = "credit_points", nullable = false)
    private Long creditPoints;

    @Column(name = "max_usages")
    private Long maxUsages;

    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    @Column(name = "is_active")
    private Boolean isActive;
}
