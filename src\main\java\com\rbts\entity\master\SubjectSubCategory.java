package com.rbts.entity.master;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "subject_sub_category")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubjectSubCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "subCategorySeqGen")
    @SequenceGenerator(name = "subCategorySeqGen", sequenceName = "sub_category_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "sub_category_name", nullable = false)
    private String subCategoryName;

    @Column(name = "sub_category_desc")
    private String subCategoryDesc;
}
