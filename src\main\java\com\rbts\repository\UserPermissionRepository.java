package com.rbts.repository;

import com.rbts.entity.auth.UserPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Set;

@Repository
public interface UserPermissionRepository extends JpaRepository<UserPermission, Long> {
    @Query(value = """
    SELECT DISTINCT up.permission_key::text
    FROM user_permissions up
    WHERE up.user_id = :userId
      AND up.is_active = true

    UNION

    SELECT DISTINCT jsonb_array_elements_text(cgp.permission_key::jsonb)
    FROM user_permission_groups upg
    LEFT JOIN custom_group_permissions cgp 
      ON upg.custom_group_key = cgp.custom_group_key
    WHERE upg.user_id = :userId
      AND upg.is_active = true
      AND cgp.is_active = true 
      AND cgp.permission_key IS NOT NULL
    """, nativeQuery = true)
    Set<String> findAllPermissionKeysByUserId(@Param("userId") Long userId);

}
