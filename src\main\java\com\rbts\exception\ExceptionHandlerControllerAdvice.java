package com.rbts.exception;

import com.fasterxml.jackson.databind.JsonMappingException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import jakarta.servlet.http.HttpServletRequest;

@ControllerAdvice
public class ExceptionHandlerControllerAdvice {

	@ExceptionHandler(ResourceNotFoundException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
   public @ResponseBody ResponseEntity<ExceptionResponse> handleResourceNotFound(final ResourceNotFoundException exception,
																				 final HttpServletRequest request) {

	ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(204).body(error);
	}


	@ExceptionHandler(DuplicateDataException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleDuplicateDataException(final DuplicateDataException exception,
																						final HttpServletRequest request) {

		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(701).body(error);
	}


	@ExceptionHandler(NullPointerException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleNullPointerException(final NullPointerException exception,
																						final HttpServletRequest request) {
		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(700).body(error);
	}



	@ExceptionHandler(ConstraintViolationException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
	public @ResponseBody ExceptionResponse handleConstraintViolation(final ConstraintViolationException exception,
																	final HttpServletRequest request) {

		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return error;
	}


	@ExceptionHandler(DataNotFoundException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleDataNotFount(final DataNotFoundException exception,
																					   final HttpServletRequest request) {

		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(404).body(error);
	}



	@ExceptionHandler(FeignClientException.class)
	@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleFeignClientException(final FeignClientException exception,
																				  final HttpServletRequest request) {

		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(702).body(error);
	}

	@ExceptionHandler(UnAuthorizedException.class)
	@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleUnAuthorizedException(final UnAuthorizedException exception,
																					  final HttpServletRequest request) {
		ExceptionResponse error = new ExceptionResponse();
		error.setErrorMessage(exception.getMessage());
		return ResponseEntity.status(401).body(error);
	}

	@ExceptionHandler(HttpMessageNotReadableException.class)
	@ResponseStatus(value = HttpStatus.BAD_REQUEST)
	public @ResponseBody ResponseEntity<ExceptionResponse> handleHttpMessageNotReadable(final HttpMessageNotReadableException exception,
																						final HttpServletRequest request) {
		ExceptionResponse error = new ExceptionResponse();
		Throwable cause = exception.getCause();
		if (cause instanceof JsonMappingException) {
			JsonMappingException jme = (JsonMappingException) cause;
			String errorMessage = jme.getPath().stream()
					.map(JsonMappingException.Reference::getFieldName)
					.findFirst()
					.orElse("Unknown field") + " is null";
			error.setErrorMessage(errorMessage);
		} else {
			error.setErrorMessage("Malformed JSON request");
		}
		return ResponseEntity.status(700).body(error);
	}
}
