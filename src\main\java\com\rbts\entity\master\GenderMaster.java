package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "gender_master")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenderMaster {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "genderSequenceGenerator")
    @SequenceGenerator(name = "genderSequenceGenerator", sequenceName = "gender_sequence", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "gender_key", nullable = false, unique = true)
    private String genderKey;

    @NotNull
    @Column(name = "gender_name", nullable = false)
    private String genderName;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
}
