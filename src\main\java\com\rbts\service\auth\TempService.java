//package com.rbts.service;
//
//import com.rbts.encryption.Base64EncryptionUtils;
//import com.rbts.entity.Temp;
//import com.rbts.repository.TempRepository;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//import jakarta.transaction.Transactional;
//import java.time.LocalDateTime;
//
//@Service
//public class TempService {
//
//    private static final Logger logger = LoggerFactory.getLogger(TempService.class);
//
//    @Autowired
//    private TempRepository tempRepository;
//
//    @Autowired
//    Base64EncryptionUtils base64EncryptionUtils;
//
//    public Temp save(Temp temp){
//       return tempRepository.save(temp);
//    }
//
//    public Temp getByUsername(String username) throws Exception {
//        return tempRepository.findFirstByUsernameOrderByCreatedAtDesc(base64EncryptionUtils.encrypt(username));
//    }
//
////    @Scheduled(cron = "0 * * * * *")
////    @Scheduled(cron = "* * * * * *")
//    @Transactional
//    public void deleteExpiredOtps() {
//        LocalDateTime expiryTime = LocalDateTime.now().minusSeconds(120); // OTP active for 100 seconds
//        logger.info("Deleting OTPs created before: " + expiryTime);
//        tempRepository.deleteByCreatedAtBefore(expiryTime);
//    }
//}
