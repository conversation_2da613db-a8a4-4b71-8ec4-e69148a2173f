package com.rbts.repository;

import com.rbts.entity.ContactDetails;
import com.rbts.entity.auth.Users;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ContactDetailsRepository extends JpaRepository<ContactDetails, Long> {

    Optional<ContactDetails> findByEmailId(String emailId);
    
    Optional<ContactDetails> findByUserId(Users user);
    
    @Query("SELECT c FROM ContactDetails c WHERE c.userId.id = :userId")
    Optional<ContactDetails> findByUserIdValue(@Param("userId") Long userId);
    
    @Query("SELECT c FROM ContactDetails c WHERE c.emailId = :emailId")
    Optional<ContactDetails> findByEmail(@Param("emailId") String emailId);
    
    @Query("SELECT c FROM ContactDetails c WHERE c.phone = :phone OR c.mobileNo = :phone")
    Optional<ContactDetails> findByPhoneNumber(@Param("phone") String phone);
    
    @Query("SELECT c FROM ContactDetails c WHERE c.isVerified = true")
    List<ContactDetails> findVerifiedContacts();
    
    @Query("SELECT c FROM ContactDetails c WHERE c.statusId.id = :statusId")
    List<ContactDetails> findByStatusId(@Param("statusId") Long statusId);
    
    @Query("SELECT c FROM ContactDetails c WHERE c.firstName LIKE %:name% OR c.lastName LIKE %:name% OR c.displayName LIKE %:name%")
    List<ContactDetails> findByNameContaining(@Param("name") String name);
    
    boolean existsByEmailId(String emailId);
    
    boolean existsByPhone(String phone);
    
    boolean existsByMobileNo(String mobileNo);
}
