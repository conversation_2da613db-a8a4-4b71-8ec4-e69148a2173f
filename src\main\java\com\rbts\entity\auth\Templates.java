package com.rbts.entity.auth;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "templates")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Templates {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "templatessequencegenerator")
    @SequenceGenerator(name = "templatessequencegenerator", sequenceName = "templatessequencegenerator",allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "template_subject", columnDefinition = "TEXT", nullable = false)
    private String templateSubject;

    @NotNull
    @Column(name = "template_header", columnDefinition = "TEXT", nullable = false)
    private String templateHeader;

    @NotNull
    @Column(name = "template_body", columnDefinition = "TEXT", nullable = false)
    private String templateBody;

    @NotNull
    @Column(name = "template_footer", columnDefinition = "TEXT", nullable = false)
    private String templateFooter;
}
