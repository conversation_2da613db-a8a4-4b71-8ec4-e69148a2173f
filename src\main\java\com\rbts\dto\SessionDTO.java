package com.rbts.dto;
import jakarta.validation.constraints.*;
import lombok.*;
import java.time.LocalDateTime;

@Data
@Builder
public class SessionDTO {
    private Long id;
    @NotNull(message = "Tutor ID is required")
    private Long tutorId;
    @NotBlank(message = "Title is required")
    private String title;
    @NotNull(message = "Start time is required")
    private LocalDateTime startTime;
    @NotNull(message = "End time is required")
    private LocalDateTime endTime;
    private boolean active;
    private String kind; // solo or group

}