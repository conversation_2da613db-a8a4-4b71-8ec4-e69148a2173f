package com.rbts.controller.auth;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rbts.dto.SignupRequest;
import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.StatusMaster;
import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.entity.auth.Users;
import com.rbts.exception.AppProperties;
import com.rbts.exception.DuplicateDataException;
import com.rbts.exception.FeignClientException;
import com.rbts.repository.PasswordsRepository;
import com.rbts.repository.UsersRepository;
import com.rbts.security.JwtUtils;
import com.rbts.service.auth.*;
import com.rbts.service.common.DynamicEntityService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.servlet.http.HttpServletResponse;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/user/oauth2")
@RequiredArgsConstructor
public class OAuth2Controller {


    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    EmailService emailService;

    @Autowired
    UsersRepository userRepository;

    @Autowired
    DynamicEntityService dynamicEntityService;



    @Autowired
    PasswordsRepository passwordsRepository;

    @Autowired
    UserRolesDetailsService userRolesDetailsService;

    @Autowired
    AppProperties appProperties;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Autowired
    UsersRepository usersRepository;

    @Autowired
    SignupService signupService;

    @GetMapping("/success")
    public void success(Authentication authentication, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("[OAuth2 Success] AUTHENTICATION: {}", authentication);

        if (authentication == null) {
            log.error("[OAuth2 Success] Authentication is null!");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"error\":\"No authentication\"}");
            return;
        }

        OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
        Map<String, Object> attributes = oauth2User.getAttributes();
        String email = (String) attributes.get("email");
        String firstName = (String) attributes.get("given_name");
        String lastName = (String) attributes.get("family_name");

        HttpSession session = request.getSession(false);
        String role = session != null ? (String) session.getAttribute("oauth2_role") : null;
        log.info("OAuth2 Success with extracted role from session: {}", role);
        log.info("OAuth2 Success with extracted role: {}", role);
        log.info("[OAuth2 Success] Extracted email: {}", email);
        log.info("[OAuth2 Success] Extracted name: {}", firstName);

        Users user = usersRepository.findByUsername(base64EncryptionUtils.encrypt(email));
        if (user == null) {
            log.info("[OAuth2 Success] No user found with email {}. Creating new user.", email);
            user = save(SignupRequest.builder()
                    .firstName(firstName)
                    .lastName(lastName)
                    .password(null)
                    .username(email)
                    .role(role)
                    .status("ACTIVE")
                    .build());
            log.info("[OAuth2 Success] Created new user with id {}", user.getId());
        } else {
            log.info("[OAuth2 Success] Found existing user with id {}", user.getId());
        }

        Set<String> permissions = Collections.emptySet();
        Map<String, Set<String>> rolePermissionsMap = null;
        try {
           rolePermissionsMap  = signupService.getPermissionsGroupedByRole(user.getId());
            permissions = rolePermissionsMap.values().stream()
                    .flatMap(Set::stream)
                    .collect(Collectors.toSet());
            log.info("[OAuth2 Success] Permissions fetched for user {}: {}", user.getId(), permissions);
        } catch (Exception e) {
            log.error("[OAuth2 Success] Failed to fetch permissions for userId {}: {}", user.getId(), e.getMessage(), e);
        }

        String jwt = jwtUtils.generateTokenFromUsername(user.getUsername(), permissions);
        log.info("[OAuth2 Success] Generated JWT for user {}", user.getId());

        // Encode values safely for URL
        String token = URLEncoder.encode(jwt, StandardCharsets.UTF_8);
        String username = URLEncoder.encode(user.getUsername(), StandardCharsets.UTF_8);
        String type = URLEncoder.encode("Bearer", StandardCharsets.UTF_8);
        String permissionStr = URLEncoder.encode(String.join(",", permissions), StandardCharsets.UTF_8);
        Long id = user.getId();

        // Redirect URL to frontend with query parameters
        String redirectUrl = String.format(
                "http://localhost:5173/#/oauth/callback?token=%s&username=%s&type=%s&id=%d&permission=%s",
                token, username, type, id,rolePermissionsMap
        );
        log.info("[OAuth2 Success] Redirecting to: {}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/failure")
    public ResponseEntity<String> failure(HttpServletRequest request) {
        Object error = request.getSession().getAttribute("SPRING_SECURITY_LAST_EXCEPTION");
        log.error("OAuth2 Failure: {}", error);
        return ResponseEntity.status(401).body("OAuth2 authentication failed. Error: " + error);
    }


    @Transactional
    public Users save(SignupRequest signUpRequest) throws Exception {
        signUpRequest.setFirstName(capitalizeFirstLetter(signUpRequest.getFirstName()));
        signUpRequest.setLastName(capitalizeFirstLetter(signUpRequest.getLastName()));
        String username = signUpRequest.getUsername();
        String role = signUpRequest.getRole().toUpperCase();
        String status = signUpRequest.getStatus();

        if (userRepository.existsByUsername(base64EncryptionUtils.encrypt(username))) {
            throw new DuplicateDataException(appProperties.getUsernameAlreadyTaken());
        }

        List<Map<String, Object>> roles = dynamicEntityService.getEntitiesByField("RoleMaster", "roleKey", role);
        List<Map<String, Object>> statusMaster = dynamicEntityService.getEntitiesByField("StatusMaster", "statusName", status);
        if (roles == null || roles.isEmpty() || statusMaster == null || statusMaster.isEmpty()) {
            throw new FeignClientException(appProperties.getFeignClient());
        }

        // Save user FIRST and ensure it's flushed so it has an ID
        Users savedUser = userRepository.saveAndFlush(Users.builder()
                .username(username)
                .password(null)
                .status(status)
                .build());

        signUpRequest.setUserId(savedUser.getId());

//        try {
            // Save role details
            userRolesDetailsService.save(UserRolesDetails.builder()
                    .user(savedUser)
                    .roleName(role)
                    .statusName(status)
                    .build());

        Map<String, Object> statusMap = statusMaster.get(0); // assuming first entry
        StatusMaster statusObj = StatusMaster.builder()
                .id(Long.parseLong(statusMap.get("id").toString()))
                .statusName(statusMap.get("statusName").toString())
                .build();

        // Convert contact details to map and save
            ContactDetails contactDetails = ContactDetails.builder()
                    .displayName(signUpRequest.getFirstName() + " " + signUpRequest.getLastName())
                    .firstName(signUpRequest.getFirstName())
                    .lastName(signUpRequest.getLastName())
                    .emailId(signUpRequest.getUsername())
                    .mobileNo(signUpRequest.getMobileNo())
                    .statusId(statusObj)
                    .userId(savedUser)
                    .build();

            Map<String, Object> contactMap = new ObjectMapper().convertValue(contactDetails, Map.class);
            dynamicEntityService.saveEntity("ContactDetails", contactMap);

//            emailService.sendNotification(NotificationDto.builder()
//                    .to(username)
//                    .username(username)
//                    .firstName(signUpRequest.getFirstName())
//                    .lastName(signUpRequest.getLastName())
//                    .build(), 1L);

            return savedUser;

//        } catch (Exception e) {
//            // Rollback user if anything else fails
//            passwordsRepository.deleteByUserId(savedUser.getId());
//            usersRepository.deleteById(savedUser.getId());
//            throw new FeignClientException(appProperties.getFeignClient());
//        }
    }

    private String capitalizeFirstLetter(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1).toUpperCase(Locale.ROOT) + name.substring(1);
    }
}
