package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

/**
 * A FieldTypeMaster.
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "field_type_master")
@SuppressWarnings("common-java:DuplicatedBlocks")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldTypeMaster extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "fieldtypemastersequencegenerator")
    @GenericGenerator(
            name = "fieldtypemastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "fieldtypemastersequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "field_type_name", nullable = false)
    private String fieldTypeName;

    @NotNull
    @Column(name = "field_type_desc", nullable = false)
    private String fieldTypeDesc;


}
