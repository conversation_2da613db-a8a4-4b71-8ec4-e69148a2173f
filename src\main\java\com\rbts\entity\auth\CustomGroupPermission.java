package com.rbts.entity.auth;

import com.rbts.service.auth.SetToJsonConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.util.HashSet;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "custom_group_permissions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomGroupPermission extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "customgrouppermissionsequencegenerator")
    @GenericGenerator(
            name = "customgrouppermissionsequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "customgrouppermissionsequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "custom_group_key", referencedColumnName = "group_key",nullable = false)
    private ClientCustomGroup customGroupKey;

    @NotNull
    @Column(name = "permission_key", nullable = false, columnDefinition = "TEXT")
    private String  permissionKeyIdJson;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @Transient
    private Set<String> permissionKeySet;

    public Set<String> getPermissionKeySet() {
        if (permissionKeyIdJson == null) {
            return new HashSet<>();
        }
        return SetToJsonConverter.convertToEntityAttribute(permissionKeyIdJson);
    }

    public void setPermissionKeySet(Set<String> permissionKeySet) {
        this.permissionKeySet = permissionKeySet;
        this.permissionKeyIdJson = SetToJsonConverter.convertToDatabaseColumn(permissionKeySet);
    }

    public void setPermissionKeyIdJson(String permissionKeyIdJson) {
        this.permissionKeyIdJson = permissionKeyIdJson;
        this.permissionKeySet = SetToJsonConverter.convertToEntityAttribute(permissionKeyIdJson);
    }
}
