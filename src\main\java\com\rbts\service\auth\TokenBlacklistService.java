package com.rbts.service.auth;

import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.exception.AppProperties;
import com.rbts.exception.UnAuthorizedException;
import com.rbts.repository.LoginDetailsRepository;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Data
@NoArgsConstructor
public class TokenBlacklistService {

    @Autowired
    AppProperties appProperties;

    private LoginDetailsRepository loginDetailsRepository;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Autowired
    public TokenBlacklistService(LoginDetailsRepository loginDetailsRepository) {
        this.loginDetailsRepository = loginDetailsRepository;
    }

    public boolean isTokenBlacklisted(String token) throws Exception {
       if (loginDetailsRepository.findByToken(token) == null ||  loginDetailsRepository.existsByTokenAndLogoutTimeIsNotNull(token)){
           throw new UnAuthorizedException();
        }else{
            return loginDetailsRepository.existsByTokenAndLogoutTimeIsNotNull(token);
        }
    }
}
