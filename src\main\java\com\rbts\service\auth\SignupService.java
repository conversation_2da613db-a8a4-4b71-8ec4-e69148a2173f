package com.rbts.service.auth;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rbts.controller.auth.AdminController;
import com.rbts.controller.auth.PublicController;
import com.rbts.dto.JwtResponse;
import com.rbts.dto.LoginRequest;
import com.rbts.dto.NotificationDto;
import com.rbts.dto.SignupRequest;
import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.StatusMaster;
import com.rbts.entity.auth.LoginDetails;
import com.rbts.entity.auth.Passwords;
import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.entity.auth.Users;
import com.rbts.exception.*;

import com.rbts.exception.NullPointerException;
import com.rbts.repository.LoginDetailsRepository;
import com.rbts.repository.PasswordsRepository;
import com.rbts.repository.UsersRepository;
import com.rbts.security.JwtUtils;
import com.rbts.service.common.DynamicEntityService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import jakarta.transaction.Transactional;

import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Service
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignupService implements AuthenticationManager {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    PasswordEncoder passwordEncoder;
    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    EmailService emailService;

    @Autowired
    UsersRepository userRepository;

    @Autowired
    DynamicEntityService dynamicEntityService;


    @Autowired
    UserPermissionService userPermissionService;

    @Autowired
    LoginDetailsRepository loginDetailsRepository;

    @Autowired
    AdminController adminController;


    @Autowired
    PasswordsRepository passwordsRepository;


    @Autowired
    UserRolesDetailsService userRolesDetailsService;



    @Autowired
    PasswordEncoder encoder;

    @Autowired
    AppProperties appProperties;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Autowired
    PublicController publicController;

    @Autowired
    PasswordsService passwordsService;

    @Autowired
    UsersRepository usersRepository;

    private static final String EMAIL_REGEX = "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$";
    private static final String MOBILE_REGEX = "^[0-9]{10}$"; // Mobile number should be exactly 10 digits

    @Transactional
    public ResponseEntity<Long> save(SignupRequest signUpRequest) throws Exception {
        signUpRequest.setFirstName(capitalizeFirstLetter(signUpRequest.getFirstName()));
        signUpRequest.setLastName(capitalizeFirstLetter(signUpRequest.getLastName()));
            String username = signUpRequest.getUsername();
            String password = signUpRequest.getPassword();
            String role = signUpRequest.getRole().toUpperCase();
            String status = signUpRequest.getStatus();
              if (userRepository.existsByUsername(base64EncryptionUtils.encrypt(username))) {
                throw new DuplicateDataException(appProperties.getUsernameAlreadyTaken());
            }
            List<Map<String, Object>> roles =  dynamicEntityService.getEntitiesByField("RoleMaster","roleKey",role);
            List<Map<String, Object>> statusMaster = dynamicEntityService.getEntitiesByField("StatusMaster","statusName",status);
            if (roles == null || statusMaster == null) {
                throw new FeignClientException(appProperties.getFeignClient());
            }
        Users user;
            if (password != null) {
                String encode = encoder.encode(password);
                user = userRepository.save(Users.builder()
                        .username(username)
                        .password(encode)
                        .status(status)
                        .build());
                signUpRequest.setUserId(user.getId());
                Passwords passwords = new Passwords();
                passwords.setUser(user);
                passwords.setPassword(encode);
                passwords.setCreatedDate(new Date());
                passwordsRepository.save(passwords);
            }
            else {
                user = userRepository.save(Users.builder()
                        .username(username)
                        .password(null)
                        .status(status)
                        .build());
                signUpRequest.setUserId(user.getId());
            }
        try {
                    userRolesDetailsService.save(UserRolesDetails.builder()
                            .user(user)
                            .roleName(role)
                            .statusName(status)
                            .build());
            Map<String, Object> statusMap = statusMaster.get(0); // assuming first match
            StatusMaster statusObj = StatusMaster.builder()
                    .id(Long.parseLong(statusMap.get("id").toString()))
                    .statusName(statusMap.get("statusName").toString())
                    .build();
            ContactDetails contactDetails = ContactDetails.builder()
                    .displayName(signUpRequest.getFirstName() + " " + signUpRequest.getLastName())
                    .firstName(signUpRequest.getFirstName())
                    .lastName(signUpRequest.getLastName())
                    .emailId(signUpRequest.getUsername())
                    .mobileNo(signUpRequest.getMobileNo())
                    .statusId(statusObj)  // <-- correct object here
                    .userId(user)
                    .build();
            Map<String, Object> contactMap = new ObjectMapper().convertValue(contactDetails, Map.class);
            dynamicEntityService.saveEntity("ContactDetails", contactMap);
            emailService.sendNotification(NotificationDto.builder()
                            .to(username)
                            .username(username)
                            .firstName(signUpRequest.getFirstName())
                            .lastName(signUpRequest.getLastName())
                            .build(), 1L);
                return ResponseEntity.status(201).body(user.getId());
        } catch (Exception e) {
            log.info("ERROR "+ e.getMessage());
                passwordsRepository.deleteByUserId(user.getId());
                usersRepository.deleteById(user.getId());
            throw new FeignClientException(appProperties.getFeignClient());
        }
    }
    private String capitalizeFirstLetter(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1).toUpperCase(Locale.ROOT) + name.substring(1);
    }
    public ResponseEntity<String> updateStatus(Long userId, String status) throws ResourceNotFoundException, FeignClientException, NullPointerException {
        if (userId == null || status == null){
            throw new NullPointerException(appProperties.getNullData());
        }
        Users users = userRepository.findOne(userId);
        if (users!=null) {
           if(dynamicEntityService.getEntitiesByField("StatusMaster","statusName",status)!=null) {
               users.setStatus(status);
               userRepository.save(users);
           }
           else throw new FeignClientException(appProperties.getFeignClient());
        } else {
            throw new ResourceNotFoundException(appProperties.getUsers());
        }
        return ResponseEntity.ok().body(appProperties.getStatusUpdated());
    }

    public ResponseEntity<String> validateUsername(String validateUsername) throws Exception {
        Users users = userRepository.findByUsername(base64EncryptionUtils.encrypt(validateUsername));
        if (users != null) {
            List<LoginDetails> loginDetails = loginDetailsRepository.findByUsername1(base64EncryptionUtils.encrypt(users.getUsername()));
            if (!loginDetails.isEmpty()) {
                throw new DuplicateDataException(appProperties.getDuplicateData());
            }
            throw new ResourceNotFoundException(appProperties.getData());
        }
        throw new NullPointerException(appProperties.getUsers());
    }

    public ResponseEntity<Object> uploadCsv(MultipartFile file, String roleId, Long companyId) throws Exception {
        if (file.isEmpty() || companyId == null) {
            throw new NullPointerException(appProperties.getData());
        }

        List<SignupRequest> signupRequests = new ArrayList<>();
        List<Map<String, String>> errorRecords = new ArrayList<>(); // Stores invalid records with error messages

        try (BufferedReader fileReader = new BufferedReader(new InputStreamReader(file.getInputStream(), "UTF-8"));
             CSVParser csvParser = new CSVParser(fileReader,
                     CSVFormat.DEFAULT.withFirstRecordAsHeader().withIgnoreHeaderCase().withTrim())) {

            Iterable<CSVRecord> csvRecords = csvParser.getRecords();

            for (CSVRecord csvRecord : csvRecords) {
                String salutation = csvRecord.get("salutation");
                String firstName = csvRecord.get("firstName");
                String lastName = csvRecord.get("lastName");
                String phoneNo = csvRecord.get("phoneNo");
                String email = csvRecord.get("email");
                String password = csvRecord.get("password");

                Map<String, String> errorMap = new HashMap<>();
                boolean isValid = true;

                // Check for empty fields
                if (firstName.isEmpty() || lastName.isEmpty() || phoneNo.isEmpty() || email.isEmpty() || password.isEmpty()) {
                    errorMap.put("error", "One or more required fields are missing.");
                    isValid = false;
                }

                // Validate email format
                if (!isValidEmailFormat(email)) {
                    errorMap.put("email", "Invalid email format: " + email);
                    isValid = false;
                }

                // Validate email domain
                if (!isDomainValid(email)) {
                    errorMap.put("emailDomain", "Invalid email domain: " + email);
                    isValid = false;
                }

                // Validate mobile number format
                if (!Pattern.matches(MOBILE_REGEX, phoneNo)) {
                    errorMap.put("mobile", "Invalid mobile number format: " + phoneNo);
                    isValid = false;
                }

                if (isValid) {
                    // Add valid data to list
                    SignupRequest signupRequest = SignupRequest.builder()
                            .firstName(firstName)
                            .lastName(lastName)
                            .mobileNo(phoneNo)
                            .salutation(salutation)
                            .username(email)
                            .password(password)
                            .role(roleId)
//                            .statusId(1L)
                            .build();
                    signupRequests.add(signupRequest);
                } else {
                    // Store invalid record with error message
                    errorMap.put("rowData", csvRecord.toString());
                    errorRecords.add(errorMap);
                }
            }

            if (!errorRecords.isEmpty()) {
                // Return errors if there are validation issues
                return ResponseEntity.status(400).body(Map.of("errors", errorRecords));
            }

            // Save valid data if no errors
            for (SignupRequest signupRequest : signupRequests) {
                save(signupRequest);
            }

            return ResponseEntity.status(201).body("CSV uploaded and data stored successfully.");
        } catch (IOException e) {
            throw new RuntimeException("Error reading the CSV file", e);
        }
    }

    private boolean isValidEmailFormat(String email) {
        Pattern pattern = Pattern.compile(EMAIL_REGEX);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    // Method to check if domain has an MX (Mail Exchange) record
    private boolean isDomainValid(String email) {
        try {
            String domain = email.substring(email.indexOf("@") + 1);

            // Setup environment for JNDI lookup
            Hashtable<String, String> env = new Hashtable<>();
            env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.dns.DnsContextFactory");
            DirContext ctx = new InitialDirContext(env);

            // Perform DNS lookup for MX records
            Attributes attrs = ctx.getAttributes(domain, new String[]{"MX"});
            Attribute attr = attrs.get("MX");

            return (attr != null && attr.size() > 0);
        } catch (NamingException e) {
            return false; // Domain does not exist
        }
    }


    public ResponseEntity signIn(LoginRequest loginRequest) throws Exception {

        if (null == loginRequest.getUsername()) {
            try {
                throw new ConstraintViolationException(appProperties.getUser());
            } catch (ConstraintViolationException e) {
                throw new RuntimeException(e);
            }
        } else if (null == loginRequest.getPassword()) {
            try {
                throw new ConstraintViolationException(appProperties.getPass());
            } catch (ConstraintViolationException e) {
                throw new RuntimeException(e);
            }
        }
        String blocked = "BLOCKED";
        Users users = userRepository.findByUsername(base64EncryptionUtils.encrypt(loginRequest.getUsername())) ;
        if (users == null ) {
            throw new UnAuthorizedException(appProperties.getInvalid());
        }
        if(users.getStatus().equalsIgnoreCase(blocked)){
            throw new UnAuthorizedException(appProperties.getAccountBlocked());
        }
        Authentication authentication = authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));
        String username = loginRequest.getUsername();
        List<LoginDetails> loginDetailsList = loginDetailsRepository.findByUsernameAndActiveIsTrue(base64EncryptionUtils.encrypt(username));
        if (!loginDetailsList.isEmpty()) {
            for (LoginDetails loginDetails1 : loginDetailsList) {
                LoginDetails loginDetails2 = loginDetailsRepository.findByUsername3(users.getUsername());
                loginDetails2.setLogoutTime(LocalDateTime.now());
                loginDetails2.setActive(false);
                loginDetailsRepository.save(loginDetails2);
            }
        }
        SecurityContextHolder.getContext().setAuthentication(authentication);
        UsersService userDetails = (UsersService) authentication.getPrincipal();
        String inActive = "INACTIVE";
        Set<String> permissions;
        Map<String, Set<String>> rolePermissionsMap = null;
        if (inActive.equalsIgnoreCase(users.getStatus())) {
            permissions = Collections.emptySet();
            log.info("User is inactive. Skipping permission fetch.");
        } else {
             rolePermissionsMap = getPermissionsGroupedByRole(userDetails.getId());
            permissions = rolePermissionsMap.values().stream()
                    .flatMap(Set::stream)
                    .collect(Collectors.toSet());
            log.info("Permissions fetched for active user.");
        }
        String jwt = jwtUtils.generateJwtToken(userDetails,permissions);
        LoginDetails loginDetails = new LoginDetails();
        loginDetails.setUsername(username);
        loginDetails.setLoginTime(LocalDateTime.now());
        loginDetails.setActive(true);
        loginDetails.setToken(jwt);
        loginDetails.setLatDetails(loginRequest.getLatDetails());
        loginDetails.setLongDetails(loginRequest.getLongDetails());
        loginDetails.setIpAddress(loginRequest.getIp_address());
        loginDetailsRepository.save(loginDetails);
        return ResponseEntity.status(201).body(JwtResponse.builder()
                .token(jwt)
                .username(base64EncryptionUtils.decrypt(userDetails.getUsername()))
                .id(userDetails.getId())
                .roles(rolePermissionsMap) // <-- add this line
                .type("Bearer")
                .build());
    }
//    public Set<String> getPermissionKeysByUserId(Long userId) throws DataNotFoundException {
//        List<UserRolesDetails> userRolesDetailsList = userRolesDetailsService.getByUserId(userId);
//        if (userRolesDetailsList == null || userRolesDetailsList.isEmpty()) {
//            throw new DataNotFoundException("No user roles found for userId: " + userId);
//        }
//        Set<String> permissionKeys = new HashSet<>();
//
//        for (UserRolesDetails userRole : userRolesDetailsList) {
//            if ("INACTIVE".equalsIgnoreCase(userRole.getStatusName())) continue;
//            String roleId = userRole.getRoleName();
//            try {
//                List<Map<String, Object>> response = dynamicEntityService.getEntitiesByField("DefaultRolePermissionsMaster", "roleKey", roleId);
//                log.info("ROLE MASTER RESPONSE : {}", response);
//                if (response != null) {
//                    for (Map<String, Object> roleMap : response) {
//                        // Check isActive at the top-level
//                        Object topIsActiveObj = roleMap.get("isActive");
//                        boolean topIsActive = topIsActiveObj instanceof Boolean && (Boolean) topIsActiveObj;
//                        if (!topIsActive) continue;
//
//                        Object permissionObj = roleMap.get("permissionKey");
//
//                        if (permissionObj != null) {
//                            // --- If POJO (PermissionMaster) ---
//                            if (permissionObj.getClass().getSimpleName().equals("PermissionMaster")) {
//                                try {
//                                    // Use reflection for POJO, if direct class not available
//                                    Object isActive = permissionObj.getClass().getMethod("getIsActive").invoke(permissionObj);
//                                    if (isActive instanceof Boolean && !(Boolean) isActive) continue;
//
//                                    Object groupTypeKey = permissionObj.getClass().getMethod("getGroupTypeKey").invoke(permissionObj);
//                                    boolean groupActive = true;
//                                    if (groupTypeKey != null && groupTypeKey.getClass().getSimpleName().contains("GroupType")) {
//                                        Object groupIsActive = groupTypeKey.getClass().getMethod("getIsActive").invoke(groupTypeKey);
//                                        groupActive = (groupIsActive instanceof Boolean) ? (Boolean) groupIsActive : true;
//                                    }
//                                    if (!groupActive) continue;
//
//                                    Object keyVal = permissionObj.getClass().getMethod("getPermissionKey").invoke(permissionObj);
//                                    if (keyVal != null) {
//                                        permissionKeys.add(keyVal.toString());
//                                        log.info("ALL PERMISSIONS IS [POJO]: {}", keyVal);
//                                    }
//                                } catch (Exception e) {
//                                    log.warn("Reflection failed for PermissionMaster POJO: {}", e.getMessage());
//                                }
//                            }
//                            // --- If Map (from Jackson etc) ---
//                            else if (permissionObj instanceof Map) {
//                                Map<?, ?> permMap = (Map<?, ?>) permissionObj;
//                                Object permIsActive = permMap.get("isActive");
//                                if (permIsActive instanceof Boolean && !(Boolean) permIsActive) continue;
//
//                                Object groupTypeObj = permMap.get("groupTypeKey");
//                                boolean groupActive = true;
//                                if (groupTypeObj instanceof Map) {
//                                    Object groupIsActive = ((Map<?, ?>) groupTypeObj).get("isActive");
//                                    groupActive = (groupIsActive instanceof Boolean) ? (Boolean) groupIsActive : true;
//                                }
//                                if (!groupActive) continue;
//
//                                Object rawKey = permMap.get("permissionKey");
//                                if (rawKey != null) {
//                                    permissionKeys.add(rawKey.toString());
//                                    log.info("ALL PERMISSIONS IS [MAP]: {}", rawKey);
//                                }
//                            }
//                            // --- If plain String (rare, fallback) ---
//                            else if (permissionObj instanceof String) {
//                                permissionKeys.add((String) permissionObj);
//                                log.info("ALL PERMISSIONS IS [STRING]: {}", permissionObj);
//                            }
//                            else {
//                                log.warn("Unknown permissionObj type: {}", permissionObj.getClass().getName());
//                            }
//                        }
//                    }
//                }
//            } catch (Exception ex) {
//                throw new DataNotFoundException("Failed to fetch role details for roleId: " + roleId + " - " + ex.getMessage());
//            }
//        }
//
//        log.info("ALL PERMISSIONS IS : {}", permissionKeys);
//        return permissionKeys;
//    }

    public Map<String, Set<String>> getPermissionsGroupedByRole(Long userId) throws DataNotFoundException {
        List<UserRolesDetails> userRolesDetailsList = userRolesDetailsService.getByUserId(userId);
        if (userRolesDetailsList == null || userRolesDetailsList.isEmpty()) {
            throw new DataNotFoundException("No user roles found for userId: " + userId);
        }

        Map<String, Set<String>> rolePermissionsMap = new HashMap<>();

        for (UserRolesDetails userRole : userRolesDetailsList) {
            if ("INACTIVE".equalsIgnoreCase(userRole.getStatusName())) continue;
            String roleId = userRole.getRoleName();

            try {
                List<Map<String, Object>> response = dynamicEntityService.getEntitiesByField("DefaultRolePermissionsMaster", "roleKey", roleId);
                log.info("ROLE MASTER RESPONSE for [{}]: {}", roleId, response);

                if (response != null) {
                    Set<String> permissionKeys = rolePermissionsMap.computeIfAbsent(roleId, k -> new HashSet<>());

                    for (Map<String, Object> roleMap : response) {
                        Object topIsActiveObj = roleMap.get("isActive");
                        boolean topIsActive = topIsActiveObj instanceof Boolean && (Boolean) topIsActiveObj;
                        if (!topIsActive) continue;

                        Object permissionObj = roleMap.get("permissionKey");
                        if (permissionObj == null) continue;

                        // POJO case
                        if (permissionObj.getClass().getSimpleName().equals("PermissionMaster")) {
                            try {
                                Object isActive = permissionObj.getClass().getMethod("getIsActive").invoke(permissionObj);
                                if (isActive instanceof Boolean && !(Boolean) isActive) continue;

                                Object groupTypeKey = permissionObj.getClass().getMethod("getGroupTypeKey").invoke(permissionObj);
                                boolean groupActive = true;
                                if (groupTypeKey != null && groupTypeKey.getClass().getSimpleName().contains("GroupType")) {
                                    Object groupIsActive = groupTypeKey.getClass().getMethod("getIsActive").invoke(groupTypeKey);
                                    groupActive = (groupIsActive instanceof Boolean) ? (Boolean) groupIsActive : true;
                                }
                                if (!groupActive) continue;

                                Object keyVal = permissionObj.getClass().getMethod("getPermissionKey").invoke(permissionObj);
                                if (keyVal != null) {
                                    permissionKeys.add(keyVal.toString());
                                    log.info("PERMISSION [{}] for role [{}] [POJO]", keyVal, roleId);
                                }
                            } catch (Exception e) {
                                log.warn("Reflection failed for POJO: {}", e.getMessage());
                            }
                        }
                        // Map case
                        else if (permissionObj instanceof Map) {
                            Map<?, ?> permMap = (Map<?, ?>) permissionObj;
                            Object permIsActive = permMap.get("isActive");
                            if (permIsActive instanceof Boolean && !(Boolean) permIsActive) continue;

                            Object groupTypeObj = permMap.get("groupTypeKey");
                            boolean groupActive = true;
                            if (groupTypeObj instanceof Map) {
                                Object groupIsActive = ((Map<?, ?>) groupTypeObj).get("isActive");
                                groupActive = (groupIsActive instanceof Boolean) ? (Boolean) groupIsActive : true;
                            }
                            if (!groupActive) continue;

                            Object rawKey = permMap.get("permissionKey");
                            if (rawKey != null) {
                                permissionKeys.add(rawKey.toString());
                                log.info("PERMISSION [{}] for role [{}] [MAP]", rawKey, roleId);
                            }
                        }
                        // String case
                        else if (permissionObj instanceof String) {
                            permissionKeys.add((String) permissionObj);
                            log.info("PERMISSION [{}] for role [{}] [STRING]", permissionObj, roleId);
                        }
                        else {
                            log.warn("Unknown permissionObj type: {}", permissionObj.getClass().getName());
                        }
                    }
                }
            } catch (Exception ex) {
                throw new DataNotFoundException("Failed to fetch role details for roleId: " + roleId + " - " + ex.getMessage());
            }
        }

        log.info("ROLE-PERMISSIONS MAP: {}", rolePermissionsMap);
        return rolePermissionsMap;
    }



    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();
        System.out.println("!!!!"+username+"  "+password);
        UserDetails user = null;
        try {
            user = userDetailsService.loadUserByUsername(base64EncryptionUtils.encrypt(username));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (passwordEncoder.matches(password, user.getPassword())) {
            return new UsernamePasswordAuthenticationToken(user, password, user.getAuthorities());
        } else {
            try {
                throw new UnAuthorizedException(appProperties.getInvalid());
            } catch (UnAuthorizedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void blockUser(Long id) {
        Users users =  usersRepository.findOne(id);
        users.setStatus("BLOCKED");
        usersRepository.save(users);
//        ContactDetails contactDetails = contactController.getByUserId(users.getId()).getContactDetails();
//        notificationController.sendEmail(
//                NotificationDto.builder()
//                        .templateId(16L)
//                        .to(users.getUsername())
//                        .firstName(contactDetails.getFirstName())
//                        .lastName(contactDetails.getLastName())
//                        .build());
    }
    public void deactivateUser(Long id) {
        Users users =  usersRepository.findOne(id);
        users.setStatus("INACTIVE");
        usersRepository.save(users);
    }

    public void activateUser(Long id) {
        Users users =  usersRepository.findOne(id);
        users.setStatus("ACTIVE");
        usersRepository.save(users);
    }
}