package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Entity
@Table(name = "tutor_availability_slots")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorAvailabilitySlot {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorAvailabilitySeqGen")
    @SequenceGenerator(name = "tutorAvailabilitySeqGen", sequenceName = "tutor_availability_slots_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails contact;

    @NotNull
    @Column(name = "day_of_week",nullable = false)
    private Long dayOfWeek;

    @NotNull
    @Column(name = "start_time",nullable = false)
    private LocalDateTime startTime;

    @NotNull
    @Column(name = "end_time",nullable = false)
    private LocalDateTime endTime;
}
