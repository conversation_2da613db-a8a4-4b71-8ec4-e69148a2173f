package com.rbts.service.auth;

import com.rbts.entity.auth.UserPermission;
import com.rbts.entity.auth.Users;
import com.rbts.repository.UserPermissionRepository;
import com.rbts.repository.UsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class UserPermissionService {

    private final UserPermissionRepository repository;
    private final UsersRepository usersRepository;

    public UserPermission create(UserPermission userPermission) {
        Users user = usersRepository.findById(userPermission.getUserId().getId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userPermission.getUserId().getId()));

        userPermission.setUserId(user);
        return repository.save(userPermission);
    }

    public UserPermission update(Long id, UserPermission updated) {
        UserPermission existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("UserPermission not found with ID: " + id));

        Users user = usersRepository.findById(updated.getUserId().getId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + updated.getUserId().getId()));

        existing.setUserId(user);
        existing.setPermissionKey(updated.getPermissionKey());
        existing.setIsActive(updated.getIsActive());

        return repository.save(existing);
    }

    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new RuntimeException("UserPermission not found with ID: " + id);
        }
        repository.deleteById(id);
    }

    public Optional<UserPermission> getById(Long id) {
        return repository.findById(id);
    }

    public List<UserPermission> getAll() {
        return repository.findAll();
    }

    public Set<String> getAllPermission (Long id) {
        return repository.findAllPermissionKeysByUserId(id);
    }
}
