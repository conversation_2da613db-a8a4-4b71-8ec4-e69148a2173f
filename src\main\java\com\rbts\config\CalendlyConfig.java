package com.rbts.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class CalendlyConfig {

    @Bean(name = "calendlyRestTemplate")
    public RestTemplate calendlyRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // Add interceptors for logging and error handling
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        
        // Add logging interceptor
        interceptors.add((request, body, execution) -> {
            System.out.println("Calendly API Request: " + request.getMethod() + " " + request.getURI());
            return execution.execute(request, body);
        });
        
        restTemplate.setInterceptors(interceptors);
        
        return restTemplate;
    }
}
