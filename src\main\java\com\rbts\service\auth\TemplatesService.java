package com.rbts.service.auth;

import com.rbts.entity.auth.Templates;
import com.rbts.repository.TemplatesRepository;
import jakarta.persistence.Tuple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TemplatesService {

    @Autowired
    TemplatesRepository templatesRepository;
    public Templates merge(Templates templates) {
        return templatesRepository.save(templates);
    }
    public Templates getTemplatesById(Long id) {
        Tuple tuple = templatesRepository.findTemplateTupleById(id);
        if (tuple == null) return null;
        Templates template = new Templates();
        template.setId(((Number) tuple.get("id")).longValue());
        template.setTemplateSubject((String) tuple.get("template_subject"));
        template.setTemplateHeader((String) tuple.get("template_header"));
        template.setTemplateBody((String) tuple.get("template_body"));
        template.setTemplateFooter((String) tuple.get("template_footer"));
        return template;
    }
    public void deleteTemplates(Long id) {
        templatesRepository.deleteById(id);
    }

    public List<Templates> getAllTemplates() {
        return templatesRepository.findAll();
    }

}
