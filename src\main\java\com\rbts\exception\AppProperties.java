package com.rbts.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@ConfigurationProperties(prefix = "authentication")
@Data
public class AppProperties {

    String secretKey;
    String padding;
    String authorization;
    String feignIds;
    String baseUrl;
    String user;
    String user1;
    String pass;
    String empId;
    String contact;
    String contactNo;
    String currentPassword;
    String newPassword;
    String employeeIdPresent;
    String refreshToken;
    String token;
    String name;
    String admin;
    String users;
    String vendor;
    String hr;
    String host;
    String port;
    String port1;
    String ssl;
    String ssl1;
    String auth;
    String auth1;
    String username;
    String password;
    String host1;
    String subject;
    String otp;
    String otpMsg;
    String otpMsg1;
    String value;
    String otpMsg2;
    String otpMsg3;
    String password1;
    String otpMsg4;
    String role1;
    String role;
    String pass1;
    String currentPassword1;
    String newPassword1;
    String data;
    String invalid;
    String duplicateData;
    String employeeId1;
    String nullData;
    String usernameAlreadyTaken;
    String roleAlreadyExist;
    String feignClient;
    String usernameNotFound;
    String superAdminUser;
    String companyNotFound;
    String unknownId;
    String invalidId;
    String unAuthorized;
    String invalidToken;
    String loggedOut;
    String loggedOutSuccess;
    String userRegisteredSuccess;
    String statusUpdated;
    Long userId;
    String employeeId;
    String vendorId;
    String accountBlocked;

}
