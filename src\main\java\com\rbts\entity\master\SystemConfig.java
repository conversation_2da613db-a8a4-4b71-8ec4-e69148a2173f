package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "system_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "systemConfigSeqGen")
    @SequenceGenerator(name = "systemConfigSeqGen", sequenceName = "system_config_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "config_key", nullable = false, unique = true)
    private String configKey;

    @NotNull
    @Column(name = "config_value", nullable = false, columnDefinition = "TEXT")
    private String configValue;

    @Column(name = "is_active")
    private Boolean isActive;
}
