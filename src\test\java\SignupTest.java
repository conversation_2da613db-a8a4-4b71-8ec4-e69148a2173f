//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.rbts.hrms.authentication.controller.PublicController;
//import com.rbts.hrms.authentication.dto.SignupRequest;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.web.servlet.MockMvc;
//
//
//import static org.mockito.Mockito.when;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
//@WebMvcTest(PublicController.class)
//class SignupTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @MockBean
//    private PublicController publicController;
//
//    private ObjectMapper objectMapper;
//
//    @BeforeEach
//    public void setup() {
//        objectMapper = new ObjectMapper();
//    }
//
//    @Test
//    public void signuptest() throws Exception {
//        SignupRequest signupRequest = SignupRequest.builder()
//                .firstName("gaurav")
//                .lastName("more")
//                .mobileNo("78787237238")
//                .username("<EMAIL>")
//                .password("gaurav@123")
//                .rolesId(1L)
//                .userId(2L)
//                .build();
//
//        when(publicController.registerUser(signupRequest))
//                .thenReturn(new ResponseEntity<>(HttpStatus.OK));
//
//        mockMvc.perform(post("/signup")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(signupRequest)))
//                .andExpect(status().isOk());
//    }
//}
