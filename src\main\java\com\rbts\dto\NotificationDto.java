package com.rbts.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NotificationDto {

    private String password;
    private String username;
    private String firstName;
    private String lastName;
    private String to;
    private String[] cc;
    private String bcc;
    private String subject;
    private int otp;
    private String companyName;

}
