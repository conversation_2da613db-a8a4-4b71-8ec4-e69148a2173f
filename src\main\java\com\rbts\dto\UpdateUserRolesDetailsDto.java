package com.rbts.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateUserRolesDetailsDto {

    @NotNull
    private Long userId;

    @NotNull
    private Long companyId;

    @NotNull
    private Set<String> roleId;

    @NotNull
    private String status;

}
