package com.rbts.service.auth;

import com.rbts.dto.UpdateUserRolesDetailsDto;
import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.exception.AppProperties;
import com.rbts.exception.DuplicateDataException;
import com.rbts.exception.NullPointerException;

import com.rbts.repository.LoginDetailsRepository;
import com.rbts.repository.UserRoleDetailsRepository;
import com.rbts.repository.UsersRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRolesDetailsService {

    @Autowired
    AppProperties appProperties;

    @Autowired
    UsersRepository usersRepository;

    @Autowired
    LoginDetailsRepository loginDetailsRepository;

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;




    @Autowired
    UserRoleDetailsRepository userRoleDetailsRepository;
    public List<UserRolesDetails> getByUserId(Long id){
    return userRoleDetailsRepository.findByUserId(id) ;
    }

    public UserRolesDetails save(UserRolesDetails userRolesDetails){
        return userRoleDetailsRepository.save(userRolesDetails);

    }

    public UpdateUserRolesDetailsDto update(UpdateUserRolesDetailsDto userRolesDetailsDto) throws DuplicateDataException, NullPointerException {
        if (userRolesDetailsDto.getRoleId() == null || userRolesDetailsDto.getUserId() == null || userRolesDetailsDto.getStatus() == null || userRolesDetailsDto.getCompanyId() == null){
            throw new NullPointerException(appProperties.getNullData());
        }
        List<UserRolesDetails> existingUserRolesDetails = userRoleDetailsRepository.findByUserId(userRolesDetailsDto.getUserId());

        // Check if any of the roles in userRolesDetailsDto.getRoleId() already exist in the user's roles
        boolean hasDuplicateRole = existingUserRolesDetails.stream()
                .map(UserRolesDetails::getRoleName)
                .anyMatch(userRolesDetailsDto.getRoleId()::contains);
        if (hasDuplicateRole) {
            throw new DuplicateDataException(appProperties.getRoleAlreadyExist());
        }
        userRolesDetailsDto.getRoleId().forEach(roleId -> {
            UserRolesDetails userRolesDetails1 = UserRolesDetails.builder()
                    .user(existingUserRolesDetails.stream().findFirst().get().getUser())  // Assuming there is a getUser() method in UserRolesDetailsDto
//                    .company(userRolesDetailsDto.getCompanyId())
                    .roleName(roleId)
                    .statusName(userRolesDetailsDto.getStatus())
                    .build();
            save(userRolesDetails1);  // Assuming save is a method to persist UserRolesDetails entity
        });
        return userRolesDetailsDto;
         }
         public List<UserRolesDetails> findByRoleId(Long id){
        return userRoleDetailsRepository.findByRoleMaster(id) ;
    }

//    public void deActivateUser(Long id) {
//        // Fetch user role details based on user ID
//        List<UserRolesDetails> userRolesDetails = userRoleDetailsRepository.findByUserId(id);
//        Users users = usersRepository.findById(id)
//                .orElseThrow(() -> new IllegalArgumentException("User not found for ID: " + id));
//        // Collect email addresses for CC
//        List<String> emailList = new ArrayList<>();
//        String firstName = null;
//        String lastName = null;
//        StringJoiner companyNamesJoiner = new StringJoiner(", ");
//
//        // Update each role's status and collect primary contact emails
//        for (UserRolesDetails userRoleDetails : userRolesDetails) {
//            userRoleDetails.setStatusName("DEACTIVATE");  // Deactivate role
//            userRoleDetailsRepository.save(userRoleDetails);  // Save updated role
//        }
//    }


    public Long validateLoginDetails(String username) throws Exception {
      return loginDetailsRepository.countByUsername(base64EncryptionUtils.encrypt(username));
    }
}
