package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;

@Entity
@Table(name = "coupons")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Coupons {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "couponSeqGen")
    @SequenceGenerator(name = "couponSeqGen", sequenceName = "couponSeqGen", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "coupon_code", nullable = false, unique = true)
    private String couponCode;

    @NotNull
    @Column(name = "credit_points", nullable = false)
    private Long creditPoints;

    @Column(name = "max_usages")
    private Long maxUsages;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "expiry_date")
    private LocalDate expiryDate;

    @Column(name = "is_active")
    private Boolean isActive;
}
