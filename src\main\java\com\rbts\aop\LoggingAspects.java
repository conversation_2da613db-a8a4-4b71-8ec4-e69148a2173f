package com.rbts.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Date;


@Aspect
@Configuration
@Component
public class LoggingAspects {

    private Logger log = LoggerFactory.getLogger(LoggingAspects.class);

    @Before(value = "execution(* com.rbts.controller..*(..))")
    public void beforeAdvice(JoinPoint joinPoint) {
        log.info("Request to" + joinPoint.getSignature() + "started at" + new Date());
    }

    @After(value = "execution(* com.rbts.service..*(..))")
    public void afterAdvice(JoinPoint joinPoint) {
        log.info("Request to" + joinPoint.getSignature() + "ended at" + new Date());
    }

}
