package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "session_repeat_frequency")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionRepeatFrequency {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "repeatFreqSeqGen")
    @SequenceGenerator(name = "repeatFreqSeqGen", sequenceName = "repeat_frequency_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "repeat_frequency_key", nullable = false, unique = true)
    private String repeatFrequencyKey;

    @NotNull
    @Column(name = "repeat_frequency_value", nullable = false)
    private String repeatFrequencyValue;

    @Column(name = "is_active")
    private Boolean isActive;
}
