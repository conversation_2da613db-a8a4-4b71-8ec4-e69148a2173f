package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "settlement_cycle")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SettlementCycle {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "settlementCycleSeqGen")
    @SequenceGenerator(name = "settlementCycleSeqGen", sequenceName = "settlement_cycle_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "settlement_cycle_key", nullable = false, unique = true)
    private String settlementCycleKey;

    @NotNull
    @Column(name = "settlement_cycle_name", nullable = false)
    private String settlementCycleName;
}
