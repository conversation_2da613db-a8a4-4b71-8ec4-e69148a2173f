{"info": {"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "name": "Generated Postman Collection"}, "item": [null, {"name": "AccountType", "item": [{"name": "Save AccountType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"accountTypeId\":0,\"accountTypeKey\":\"gJm7hlCKYs\",\"accountTypeDesc\":\"v2rLqSSKeZ\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/AccountType/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "save"]}}}, {"name": "Update AccountType", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"accountTypeId\":0,\"accountTypeKey\":\"f1oL0aYP5l\",\"accountTypeDesc\":\"IL4UGPLC3L\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/AccountType/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "update", "{value}"]}}}, {"name": "ReadById AccountType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/AccountType/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "{value}"]}}}, {"name": "GetAll AccountType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/AccountType/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "list"]}}}, {"name": "Dynamic Query AccountType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter AccountType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/AccountType/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "filter", "{key}", "{value}"]}}}, {"name": "Sort AccountType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/AccountType/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial AccountType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"accountTypeId\":0,\"accountTypeKey\":\"4A9aiw0CUE\",\"accountTypeDesc\":\"QG384Ng7F3\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/AccountType/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "AccountType", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "BillingRateType", "item": [{"name": "Save BillingRateType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"billingRateTypeId\":0,\"billingRateTypeKey\":\"wUzmz53oDj\",\"billingRateType\":\"O4mI564YlZ\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/BillingRateType/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "save"]}}}, {"name": "Update BillingRateType", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"billingRateTypeId\":0,\"billingRateTypeKey\":\"qJwqISRPEP\",\"billingRateType\":\"zNIAaPK1hk\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/BillingRateType/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "update", "{value}"]}}}, {"name": "ReadById BillingRateType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/BillingRateType/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "{value}"]}}}, {"name": "GetAll BillingRateType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/BillingRateType/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "list"]}}}, {"name": "Dynamic Query BillingRateType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter BillingRateType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/BillingRateType/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "filter", "{key}", "{value}"]}}}, {"name": "Sort BillingRateType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/BillingRateType/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial BillingRateType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"billingRateTypeId\":0,\"billingRateTypeKey\":\"HedOsmh0G8\",\"billingRateType\":\"ZVCLvTCge4\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/BillingRateType/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BillingRateType", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "Coupons", "item": [{"name": "Save Coupons", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"couponId\":0,\"couponCode\":\"QSEfBzyjW1\",\"creditPoints\":0,\"maxUsages\":0}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/Coupons/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "save"]}}}, {"name": "Update Coupons", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"couponId\":0,\"couponCode\":\"zOpiGFf5ZF\",\"creditPoints\":0,\"maxUsages\":0,\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/Coupons/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "update", "{value}"]}}}, {"name": "ReadById Coupons", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/Coupons/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "{value}"]}}}, {"name": "GetAll Coupons", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/Coupons/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "list"]}}}, {"name": "Dynamic Query Coupons", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Coupons", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/Coupons/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "filter", "{key}", "{value}"]}}}, {"name": "Sort Coupons", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/Coupons/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Coupons", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"couponId\":0,\"couponCode\":\"34UNwHv3qG\",\"creditPoints\":0,\"maxUsages\":0}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/Coupons/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupons", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "GenderMaster", "item": [{"name": "Save GenderMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"id\":0,\"genderKey\":\"vDCSzWviq4\",\"genderName\":\"wnoIZQtkj1\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/GenderMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "save"]}}}, {"name": "Update GenderMaster", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"id\":\"{value}\",\"genderKey\":\"XWOgoxq0OG\",\"genderName\":\"jrsOI2mYas\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/GenderMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "update", "{value}"]}}}, {"name": "ReadById GenderMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/GenderMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "{value}"]}}}, {"name": "GetAll GenderMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/GenderMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "list"]}}}, {"name": "Dynamic Query GenderMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter <PERSON>", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/GenderMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort GenderMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/GenderMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial GenderMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"id\":0,\"genderKey\":\"vmBfJrYnQD\",\"genderName\":\"m1V9reqnWv\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/GenderMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "GenderMaster", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SessionLocation", "item": [{"name": "Save SessionLocation", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionLocationId\":0,\"locationKey\":\"Vws7qTHj90\",\"locationValue\":\"fXfiEJlc1a\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionLocation/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "save"]}}}, {"name": "Update SessionLocation", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionLocationId\":0,\"locationKey\":\"KTCOLmPfRl\",\"locationValue\":\"JQFxs5J4hM\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionLocation/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "update", "{value}"]}}}, {"name": "ReadById SessionLocation", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionLocation/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "{value}"]}}}, {"name": "GetAll SessionLocation", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionLocation/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "list"]}}}, {"name": "Dynamic Query SessionLocation", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SessionLocation", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionLocation/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionLocation", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionLocation/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionLocation", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionLocationId\":0,\"locationKey\":\"aBoIT7pJxN\",\"locationValue\":\"uzC2ZqToe6\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionLocation/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SessionRepeatFrequency", "item": [{"name": "Save SessionRepeatFrequency", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"repeatFrequencyId\":0,\"repeatFrequencyKey\":\"TBfh2ldRaG\",\"repeatFrequencyValue\":\"H9NxHIToaO\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "save"]}}}, {"name": "Update SessionRepeatFrequency", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"repeatFrequencyId\":0,\"repeatFrequencyKey\":\"CSBWZRAKSL\",\"repeatFrequencyValue\":\"FKNDhjoSuu\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "update", "{value}"]}}}, {"name": "ReadById SessionRepeatFrequency", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "{value}"]}}}, {"name": "GetAll SessionRepeatFrequency", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "list"]}}}, {"name": "Dynamic Query SessionRepeatFrequency", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SessionRepeatFrequency", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionRepeatFrequency", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionRepeatFrequency", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"repeatFrequencyId\":0,\"repeatFrequencyKey\":\"GfZMBhplBE\",\"repeatFrequencyValue\":\"De2GEUIK2N\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionRepeatFrequency/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRepeatFrequency", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SessionType", "item": [{"name": "Save SessionType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionTypeId\":0,\"sessionTypeKey\":\"GCIhGOQkzs\",\"sessionTypeDesc\":\"tdlnACEoBW\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionType/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "save"]}}}, {"name": "Update SessionType", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionTypeId\":0,\"sessionTypeKey\":\"Bh5YgLGgww\",\"sessionTypeDesc\":\"bvlAz5oPIA\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionType/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "update", "{value}"]}}}, {"name": "ReadById SessionType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionType/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "{value}"]}}}, {"name": "GetAll SessionType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionType/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "list"]}}}, {"name": "Dynamic Query SessionType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SessionType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionType/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionType", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SessionType/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionType", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"sessionTypeId\":0,\"sessionTypeKey\":\"kIhTFXBs9M\",\"sessionTypeDesc\":\"aozTX44Mg5\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SessionType/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionType", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SubjectMaster", "item": [{"name": "Save SubjectMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subjectId\":0,\"subjectKey\":\"0WVRxqHcD0\",\"subjectShortName\":\"Jwzm10mcNQ\",\"subjectDescription\":\"FEbBkYnznB\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "save"]}}}, {"name": "Update SubjectMaster", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subjectId\":0,\"subjectKey\":\"XbleeuB1fV\",\"subjectShortName\":\"rU8e3wZ0AM\",\"subjectDescription\":\"lREvakBUfI\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "update", "{value}"]}}}, {"name": "ReadById SubjectMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "{value}"]}}}, {"name": "GetAll SubjectMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "list"]}}}, {"name": "Dynamic Query SubjectMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SubjectMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort SubjectMaster", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SubjectMaster", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subjectId\":0,\"subjectKey\":\"TpIrwt9lWl\",\"subjectShortName\":\"fqQMzAYXn9\",\"subjectDescription\":\"tZMXzYRTt6\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectMaster", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SubjectSubCategory", "item": [{"name": "Save SubjectSubCategory", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subCategoryId\":0,\"subCategoryName\":\"EIkFKAUQBp\",\"subCategoryDesc\":\"J8MXWlAJn4\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectSubCategory/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "save"]}}}, {"name": "Update SubjectSubCategory", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subCategoryId\":0,\"subCategoryName\":\"5LpjzVJapq\",\"subCategoryDesc\":\"ymK82GefQy\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectSubCategory/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "update", "{value}"]}}}, {"name": "ReadById SubjectSubCategory", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectSubCategory/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "{value}"]}}}, {"name": "GetAll SubjectSubCategory", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectSubCategory/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "list"]}}}, {"name": "Dynamic Query SubjectSubCategory", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SubjectSubCategory", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectSubCategory/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "filter", "{key}", "{value}"]}}}, {"name": "Sort SubjectSubCategory", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SubjectSubCategory/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SubjectSubCategory", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"subCategoryId\":0,\"subCategoryName\":\"ExTQ2jcce3\",\"subCategoryDesc\":\"XE6YY0C3XN\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SubjectSubCategory/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SubjectSubCategory", "updatePartialEntity", "{id}"]}}}]}, null, {"name": "SystemConfig", "item": [{"name": "Save SystemConfig", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"configId\":0,\"configKey\":\"lZr15P6mZX\",\"configValue\":\"2wQbcVtYen\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SystemConfig/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "save"]}}}, {"name": "Update SystemConfig", "request": {"method": "PUT", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"configId\":0,\"configKey\":\"TOZQ6V0z0m\",\"configValue\":\"BlLQjFO6DY\",\"id\":\"{value}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SystemConfig/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "update", "{value}"]}}}, {"name": "ReadById SystemConfig", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SystemConfig/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "{value}"]}}}, {"name": "GetAll SystemConfig", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SystemConfig/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "list"]}}}, {"name": "Dynamic Query SystemConfig", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SystemConfig", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SystemConfig/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "filter", "{key}", "{value}"]}}}, {"name": "Sort SystemConfig", "request": {"method": "GET", "header": [{"value": "application/json", "key": "Content-Type"}], "url": {"raw": "http://localhost:9766/SystemConfig/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SystemConfig", "request": {"method": "POST", "header": [{"value": "application/json", "key": "Content-Type"}], "body": {"raw": "{\"configId\":0,\"configKey\":\"vtuamPKTF8\",\"configValue\":\"zF0tR8L5hR\"}", "mode": "raw"}, "url": {"raw": "http://localhost:9766/SystemConfig/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SystemConfig", "updatePartialEntity", "{id}"]}}}]}]}