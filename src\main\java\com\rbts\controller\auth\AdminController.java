package com.rbts.controller.auth;



import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.entity.auth.Users;
import com.rbts.exception.*;
import com.rbts.repository.UsersRepository;
import com.rbts.security.JwtUtils;
import com.rbts.service.auth.SignupService;
import com.rbts.service.auth.UserRolesDetailsService;
import com.rbts.exception.NullPointerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/auth")
@Validated
@Slf4j
public class  AdminController {

    @Autowired
    SignupService signupService;

    @Autowired
    UserRolesDetailsService userRolesDetailsService;

    @Autowired
    UsersRepository usersRepository;

    @Autowired
    JwtUtils jwtUtils;

    @PostMapping("/upload-csv/{roleKey}/{companyId}")
    public ResponseEntity<Object> uploadCSV(@RequestParam("file") MultipartFile file, @PathVariable String roleKey, @PathVariable Long companyId) throws Exception {
        return signupService.uploadCsv(file,roleKey,companyId);
    }

    @GetMapping("/getByRoleId/{id}")
    public List<Long> getByRoleId(@PathVariable Long id) {
        List<Long> userIds = new ArrayList<>();
        System.out.println("path "+id);
        List<UserRolesDetails> userRolesDetails = userRolesDetailsService.findByRoleId(id);
        log.info("data : "+userRolesDetails);
        for (UserRolesDetails userRoleDetail : userRolesDetails) {
            if (userRoleDetail.getUser() != null && userRoleDetail.getUser().getId() != null) {
                userIds.add(userRoleDetail.getUser().getId());
            }
        }
        log.info("UserId :" + userIds );
        return userIds;
    }

    @Transactional
    @PostMapping ("/update/userStatus/{userId}/{statusId}")
    public ResponseEntity<String> updateStatus(@PathVariable Long userId, @PathVariable String status ) throws ResourceNotFoundException, FeignClientException, NullPointerException {
        return signupService.updateStatus(userId,status);
    }

    @GetMapping("/validateToken/{token}")
    public boolean validateToken(@PathVariable String token) throws Exception {
        return jwtUtils.validateJwtToken(token);
    }

    @GetMapping("/loginDetails/{username}")
    public Long validateLoginDetails(@PathVariable(name = "username") String username) throws Exception {
        return userRolesDetailsService.validateLoginDetails(username);
    }

//    @PutMapping("/updateUserRoleDetails")
//    public UpdateUserRolesDetailsDto updateUserRoleDetails(@RequestBody UpdateUserRolesDetailsDto userRolesDetailsDto) throws DuplicateDataException, NullPointerException {
//        return userRolesDetailsService.update(userRolesDetailsDto);
//    }


    @PutMapping("/deActivateUser/{userId}")
    public void deActivateUser(@PathVariable ("userId" )Long id){
        signupService.deactivateUser(id);
    }

    @PutMapping("/activateUser/{userId}")
    public void activateUser(@PathVariable ("userId" )Long id){
        signupService.activateUser(id);
    }

    @PutMapping("/blockUser/{userId}")
    public void blockUser(@PathVariable(name = "userId") Long id){
        signupService.blockUser(id);
    }

    @GetMapping("/getUserById/{id}")
    public Users getUserById(@PathVariable Long id){
        return usersRepository.findOne(id);
    }
    @GetMapping("/getPermissionKeysByUserId/{id}")
    public Map<String, Set<String>> getPermissionKeysByUserId(@PathVariable Long id){
        try {
            return signupService.getPermissionsGroupedByRole(id);
        } catch (DataNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/saveUserRolesDetails")
    public UserRolesDetails saveUserRoleDetails(@RequestBody UserRolesDetails userRolesDetails)  {
        return userRolesDetailsService.save(userRolesDetails);
    }



}
