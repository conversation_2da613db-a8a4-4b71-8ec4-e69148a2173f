package com.rbts.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.io.Serializable;

/**
 * A MasterConfig.
 */
@Entity
@Table(name = "master_config")
@SuppressWarnings("common-java:DuplicatedBlocks")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MasterConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "field_name", nullable = false)
    private String fieldName;

    @NotNull
    @Column(name = "field_label", nullable = false)
    private String fieldLabel;

    @NotNull
    @Column(name = "mapped_dto_field", nullable = false)
    private String mappedDtoField;

    @NotNull
    @Column(name = "is_mandatory", nullable = false)
    private Boolean isMandatory;

    @Column(name = "max_length")
    private Long maxLength;

    @Column(name = "min_length")
    private Long minLength;

    @NotNull
    @Column(name = "is_unique", nullable = false)
    private Boolean isUnique;

    @Column(name = "validation_regex")
    private String validationRegex;

    @Column(name = "option_label")
    private String optionLabel;

    @Column(name = "get_api_url")
    private String getApiUrl;

    @NotNull
    @Column(name = "display_preference", nullable = false)
    private Long displayPreference;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "masterConfigs" }, allowSetters = true)
    private MasterManager masterManager;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "masterConfigs" }, allowSetters = true)
    private FieldTypeMaster fieldTypeMaster;

}
