package com.rbts.repository;

import com.rbts.entity.CalendlyOrganizationMemberDetails;
import com.rbts.entity.ContactDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CalendlyOrganizationMemberDetailsRepository extends JpaRepository<CalendlyOrganizationMemberDetails, Long> {
    
    Optional<CalendlyOrganizationMemberDetails> findByUserEmail(String userEmail);
    
    Optional<CalendlyOrganizationMemberDetails> findByContact(ContactDetails contact);
    
    List<CalendlyOrganizationMemberDetails> findByOrganizationUri(String organizationUri);
    
    @Query("SELECT c FROM CalendlyOrganizationMemberDetails c WHERE c.organizationUri = :organizationUri AND c.active = true")
    List<CalendlyOrganizationMemberDetails> findActiveByOrganizationUri(@Param("organizationUri") String organizationUri);
    
    @Query("SELECT c FROM CalendlyOrganizationMemberDetails c WHERE c.organizationUri = :organizationUri AND c.invitationStatus = :status")
    List<CalendlyOrganizationMemberDetails> findByOrganizationUriAndInvitationStatus(
            @Param("organizationUri") String organizationUri, 
            @Param("status") String status);
    
    @Query("SELECT c FROM CalendlyOrganizationMemberDetails c WHERE c.organizationUri = :organizationUri AND c.role = :role AND c.active = true")
    List<CalendlyOrganizationMemberDetails> findActiveByOrganizationUriAndRole(
            @Param("organizationUri") String organizationUri, 
            @Param("role") String role);
    
    @Query("SELECT COUNT(c) FROM CalendlyOrganizationMemberDetails c WHERE c.organizationUri = :organizationUri AND c.active = true")
    Long countActiveByOrganizationUri(@Param("organizationUri") String organizationUri);
    
    @Query("SELECT COUNT(c) FROM CalendlyOrganizationMemberDetails c WHERE c.organizationUri = :organizationUri AND c.invitationStatus = 'accepted'")
    Long countAcceptedByOrganizationUri(@Param("organizationUri") String organizationUri);

}
