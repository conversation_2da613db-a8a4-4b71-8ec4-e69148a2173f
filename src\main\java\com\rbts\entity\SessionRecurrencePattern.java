package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "session_recurrence_patterns")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionRecurrencePattern {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "recurrencePatternSeqGen")
    @SequenceGenerator(name = "recurrencePatternSeqGen", sequenceName = "session_recurrence_pattern_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "frequency", nullable = false, length = 10)
    private String frequency;

    @NotNull
    @Column(name = "interval", nullable = false)
    private Long interval = 1L;

    @Column(name = "day_of_week")
    private Long dayOfWeek;

    @Column(name = "week_of_month")
    private Long weekOfMonth;

    @Column(name = "day_of_month")
    private Long dayOfMonth;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "occurrence_count")
    private Long occurrenceCount;
}
