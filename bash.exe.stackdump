Stack trace:
Frame         Function      Args
0007FFFF7F40  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6E40) msys-2.0.dll+0x1FE8E
0007FFFF7F40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF8218) msys-2.0.dll+0x67F9
0007FFFF7F40  000210046832 (000210286019, 0007FFFF7DF8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7F40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7F40  000210068E24 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF8220  00021006A225 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCFDEC0000 ntdll.dll
7FFCFD500000 KERNEL32.DLL
7FFCFB3B0000 KERNELBASE.dll
7FFCFC350000 USER32.dll
7FFCFB8F0000 win32u.dll
7FFCFDD40000 GDI32.dll
7FFCFB270000 gdi32full.dll
7FFCFB780000 msvcp_win.dll
7FFCFB920000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCFD790000 advapi32.dll
7FFCFDDD0000 msvcrt.dll
7FFCFD850000 sechost.dll
7FFCFC700000 RPCRT4.dll
7FFCFA640000 CRYPTBASE.DLL
7FFCFB1D0000 bcryptPrimitives.dll
7FFCFC310000 IMM32.DLL
