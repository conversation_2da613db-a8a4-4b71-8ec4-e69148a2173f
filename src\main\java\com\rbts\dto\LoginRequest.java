package com.rbts.dto;

import com.rbts.encryption.Encrypt;
import com.rbts.encryption.EncryptionEntityListener;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.EntityListeners;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(EncryptionEntityListener.class)
public class LoginRequest {


     @Encrypt
     @NotNull
     private String username;

     @NotNull
     private String password;

     private String latDetails;

     private String longDetails;

     private String ip_address;

}

