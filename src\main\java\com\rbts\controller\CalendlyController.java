package com.rbts.controller;

import com.rbts.dto.SessionDTO;
import com.rbts.service.CalendlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/calendly")
public class CalendlyController {

    @Autowired
    private CalendlyService calendlyService;

    @PostMapping("/event-types")
    public ResponseEntity<SessionDTO> createEventType(@Valid @RequestBody SessionDTO sessionDTO) {
        return ResponseEntity.ok(calendlyService.createEventType(sessionDTO));
    }

    @GetMapping("/event-types/{id}")
    public ResponseEntity<SessionDTO> getEventType(@PathVariable Long id) {
        return ResponseEntity.ok(calendlyService.getEventType(id));
    }

    @GetMapping("/event-types/tutor/{tutorId}")
    public ResponseEntity<List<SessionDTO>> getTutorEventTypes(@PathVariable Long tutorId) {
        return ResponseEntity.ok(calendlyService.getTutorEventTypes(tutorId));
    }

    @PutMapping("/event-types/{id}")
    public ResponseEntity<SessionDTO> updateEventType(@PathVariable Long id, @Valid @RequestBody SessionDTO sessionDTO) {
        return ResponseEntity.ok(calendlyService.updateEventType(id, sessionDTO));
    }

    @DeleteMapping("/event-types/{id}")
    public ResponseEntity<Void> deleteEventType(@PathVariable Long id) {
        calendlyService.deleteEventType(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/tutors/{tutorId}/availability")
    public ResponseEntity<List<AvailabilityDTO>> getTutorAvailability(
            @PathVariable Long tutorId,
            @RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        return ResponseEntity.ok(calendlyService.getTutorAvailability(tutorId, startTime, endTime));
    }

    @PostMapping("/bookings")
    public ResponseEntity<BookingDTO> scheduleSession(@Valid @RequestBody BookingDTO bookingDTO) {
        return ResponseEntity.ok(calendlyService.scheduleSession(bookingDTO));
    }

    @PostMapping("/organization/invite")
    public ResponseEntity<CalendlyOrganizationMemberDetails> createOrganizationInvite(@RequestParam String email) {
        return ResponseEntity.ok(calendlyService.createOrganizationInvite(email));
    }

    @PostMapping("/organization/invite/check-status")
    public ResponseEntity<CalendlyOrganizationMemberDetails> checkInviteStatus(@RequestParam String email) {
        return ResponseEntity.ok(calendlyService.checkAndUpdateInviteStatus(email));
    }
}