package com.rbts.controller;

import com.rbts.dto.AvailabilityDTO;
import com.rbts.dto.BookingDTO;
import com.rbts.dto.SessionDTO;
import com.rbts.entity.CalendlyOrganizationMemberDetails;
import com.rbts.service.CalendlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/calendly")
public class CalendlyController {

    @Autowired
    private CalendlyService calendlyService;

    @PostMapping("/event-types")
    public ResponseEntity<SessionDTO> createEventType(@Valid @RequestBody SessionDTO sessionDTO) {
        return ResponseEntity.ok(calendlyService.createEventType(sessionDTO));
    }

    @GetMapping("/event-types/{id}")
    public ResponseEntity<SessionDTO> getEventType(@PathVariable Long id) {
        return ResponseEntity.ok(calendlyService.getEventType(id));
    }

    @GetMapping("/event-types/tutor/{tutorId}")
    public ResponseEntity<List<SessionDTO>> getTutorEventTypes(@PathVariable Long tutorId) {
        return ResponseEntity.ok(calendlyService.getTutorEventTypes(tutorId));
    }

    @PutMapping("/event-types/{id}")
    public ResponseEntity<SessionDTO> updateEventType(@PathVariable Long id, @Valid @RequestBody SessionDTO sessionDTO) {
        return ResponseEntity.ok(calendlyService.updateEventType(id, sessionDTO));
    }

    @DeleteMapping("/event-types/{id}")
    public ResponseEntity<Void> deleteEventType(@PathVariable Long id) {
        calendlyService.deleteEventType(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/tutors/{tutorId}/availability")
    public ResponseEntity<List<AvailabilityDTO>> getTutorAvailability(
            @PathVariable Long tutorId,
            @RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        return ResponseEntity.ok(calendlyService.getTutorAvailability(tutorId, startTime, endTime));
    }

    @PostMapping("/bookings")
    public ResponseEntity<BookingDTO> scheduleSession(@Valid @RequestBody BookingDTO bookingDTO) {
        return ResponseEntity.ok(calendlyService.scheduleSession(bookingDTO));
    }

    @PostMapping("/organization/invite")
    public ResponseEntity<CalendlyOrganizationMemberDetails> createOrganizationInvite(@RequestParam String email) {
        return ResponseEntity.ok(calendlyService.createOrganizationInvite(email));
    }

    @PostMapping("/organization/invite/check-status")
    public ResponseEntity<CalendlyOrganizationMemberDetails> checkInviteStatus(@RequestParam String email) {
        return ResponseEntity.ok(calendlyService.checkAndUpdateInviteStatus(email));
    }

    @GetMapping("/test/health")
    public ResponseEntity<String> testHealth() {
        return ResponseEntity.ok("Calendly API integration is working!");
    }

    @GetMapping("/test/config")
    public ResponseEntity<String> testConfig() {
        try {
            // This will test if the configuration is properly loaded
            return ResponseEntity.ok("Calendly API URL configured and service is ready");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Configuration error: " + e.getMessage());
        }
    }
}