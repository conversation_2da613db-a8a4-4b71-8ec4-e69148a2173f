package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "role_master")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleMaster extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "rolemastersequencegenerator")
    @GenericGenerator(
            name = "rolemastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @Parameter(name = "sequence_name", value = "rolemastersequencegenerator"),
                    @Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "role_key", nullable = false, length = 50, unique = true)
    private String roleKey;

    @NotNull
    @Column(name = "role_name", nullable = false, length = 50, unique = true)
    private String roleName;

    @NotNull
    @Column(name = "role_desc", columnDefinition = "TEXT")
    private String roleDesc;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
}
