package com.rbts.repository;

import com.rbts.entity.auth.Users;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UsersRepository extends JpaRepository<Users, Long> {

    @Query("SELECT u FROM Users u WHERE u.id = :id")
    Users findOne(@Param("id") Long data);

    // @Query("SELECT u FROM Users u LEFT JOIN u.roles r WHERE r.name = :roleName")
    // Users findByRoleName(@Param("roleName") String roleName);

    @Query("SELECT u FROM Users u WHERE u.username = :username")
    Users findByUsername(@Param("username") String username);

    Boolean existsByUsername(String username);

    // List<Users> findByStatus(Integer status);
}
