{"info": {"name": "Generated Postman Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Booking", "item": [{"name": "Save Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"session\":{\"id\":0,\"title\":\"OQyL4KVcL2\",\"description\":\"Kc3LdRUSew\",\"sessionPrice\":0,\"currencyCode\":\"rc1Jf8br6X\",\"minStudents\":0,\"maxStudents\":0},\"students\":{\"id\":0,\"salutation\":\"eMw6qsOqfO\",\"firstName\":\"WSkqZF98qG\",\"lastName\":\"pXcT23OWrn\",\"displayName\":\"mdwqYmme4h\",\"avatarImgPath\":\"ydSdT6S5EB\",\"phone\":\"Wd4pGSPYTw\",\"emailId\":\"JWhh0pS0DH\",\"mobileNo\":\"PFVfIRWM8d\",\"trustPilotId\":\"cf3qN3w0Eb\",\"address\":\"vrhpK6AeMf\",\"aboutMe\":\"G4uQBO6FeX\",\"bioCoverLetter\":\"RDLB6u2HXR\",\"locateTutorBadge\":\"uRXrDQk6Kt\",\"locateTutorSummary\":\"thpJn7KAaz\",\"genderId\":\"hwlRkMvEAx\",\"currentLocation\":\"Ti20BHuv5A\",\"geoLocation\":\"iiYPKE07Ci\"},\"status\":{\"id\":0,\"statusName\":\"BQy1cZwLOy\"},\"creditPointsUsed\":0,\"recurrencePattern\":{\"id\":0,\"frequency\":\"GpIXtfdN4D\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}}"}, "url": {"raw": "http://localhost:9766/Booking/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "save"]}}}, {"name": "Update Booking", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"session\":{\"id\":0,\"title\":\"aqs9U6YqLD\",\"description\":\"ZYkp4NsJ5x\",\"sessionPrice\":0,\"currencyCode\":\"HrEbr3Bv9V\",\"minStudents\":0,\"maxStudents\":0},\"students\":{\"id\":0,\"salutation\":\"fRCMkNi38f\",\"firstName\":\"a4OUfeXzLY\",\"lastName\":\"CZRgUwCsrF\",\"displayName\":\"t7wf6mKrxv\",\"avatarImgPath\":\"AboKIM9P2M\",\"phone\":\"Rcugf0PYqP\",\"emailId\":\"a45P2vQ378\",\"mobileNo\":\"xsIoXGsB6E\",\"trustPilotId\":\"BUw7Ux1a9d\",\"address\":\"itz7xkO4EZ\",\"aboutMe\":\"ZuNf10x28A\",\"bioCoverLetter\":\"8kVyjKZn7U\",\"locateTutorBadge\":\"oymfqye0qb\",\"locateTutorSummary\":\"BrxJq7l42U\",\"genderId\":\"V6yEUUko9T\",\"currentLocation\":\"U9IAXFB9dX\",\"geoLocation\":\"1FKj6wikY3\"},\"status\":{\"id\":0,\"statusName\":\"dylVMbNdhh\"},\"creditPointsUsed\":0,\"recurrencePattern\":{\"id\":0,\"frequency\":\"GbbEdSnF8G\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}}"}, "url": {"raw": "http://localhost:9766/Booking/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "update", "{value}"]}}}, {"name": "ReadById Booking", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Booking/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "{value}"]}}}, {"name": "GetAll Booking", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Booking/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "list"]}}}, {"name": "Dynamic Query Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Booking", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Booking/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "filter", "{key}", "{value}"]}}}, {"name": "Sort Booking", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Booking/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"session\":{\"id\":0,\"title\":\"WwSVgR8Qyb\",\"description\":\"I2xsEBBk96\",\"sessionPrice\":0,\"currencyCode\":\"lyCgSYT71Q\",\"minStudents\":0,\"maxStudents\":0},\"students\":{\"id\":0,\"salutation\":\"F1xDoctq0k\",\"firstName\":\"eC2o4zSevD\",\"lastName\":\"z5UBSKE5Ro\",\"displayName\":\"kbHOOLLhwy\",\"avatarImgPath\":\"og1FmKojvn\",\"phone\":\"w0U3cjHS1f\",\"emailId\":\"a2dhcJqiDF\",\"mobileNo\":\"ANH61CyCu9\",\"trustPilotId\":\"2FovAKv82c\",\"address\":\"J7VdaLyYm5\",\"aboutMe\":\"NxeY3yva7Q\",\"bioCoverLetter\":\"e259xvYHQh\",\"locateTutorBadge\":\"dJ7PbxQUi1\",\"locateTutorSummary\":\"NWr3AKqwMZ\",\"genderId\":\"i5pLay8Cc5\",\"currentLocation\":\"4dwet5ofjD\",\"geoLocation\":\"XF3nDiRjl0\"},\"status\":{\"id\":0,\"statusName\":\"qXfvTMNFMT\"},\"creditPointsUsed\":0,\"recurrencePattern\":{\"id\":0,\"frequency\":\"LTv0uqAM88\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}}"}, "url": {"raw": "http://localhost:9766/Booking/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Booking", "updatePartialEntity", "{id}"]}}}]}, {"name": "BookingOccurrence", "item": [{"name": "Save BookingOccurrence", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"statusId\":{\"id\":0,\"statusName\":\"S6W4oaH99t\"}}"}, "url": {"raw": "http://localhost:9766/BookingOccurrence/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "save"]}}}, {"name": "Update BookingOccurrence", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"statusId\":{\"id\":0,\"statusName\":\"Q9kwfamv3I\"}}"}, "url": {"raw": "http://localhost:9766/BookingOccurrence/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "update", "{value}"]}}}, {"name": "ReadById BookingOccurrence", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/BookingOccurrence/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "{value}"]}}}, {"name": "GetAll BookingOccurrence", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/BookingOccurrence/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "list"]}}}, {"name": "Dynamic Query BookingOccurrence", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter BookingOccurrence", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/BookingOccurrence/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "filter", "{key}", "{value}"]}}}, {"name": "Sort BookingOccurrence", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/BookingOccurrence/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial BookingOccurrence", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"statusId\":{\"id\":0,\"statusName\":\"Qy06oFfKbj\"}}"}, "url": {"raw": "http://localhost:9766/BookingOccurrence/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "BookingOccurrence", "updatePartialEntity", "{id}"]}}}]}, {"name": "ContactDetails", "item": [{"name": "Save ContactDetails", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"salutation\":\"2jsZQiDAy4\",\"firstName\":\"s9zkYVVmox\",\"lastName\":\"VrG2Fd7Yje\",\"displayName\":\"XJrXh4liNM\",\"avatarImgPath\":\"pAt36WtJIs\",\"phone\":\"5zWT2PoWJ1\",\"emailId\":\"XZXmFhTVCB\",\"mobileNo\":\"ITlJVgm6Hm\",\"trustPilotId\":\"Rj0K8q46NX\",\"address\":\"QufTxSSNfx\",\"aboutMe\":\"X0QKSOAUaE\",\"bioCoverLetter\":\"bXr3IoSGcE\",\"locateTutorBadge\":\"r1afxaROxj\",\"locateTutorSummary\":\"Dlx2MOCm9y\",\"genderId\":\"NmSBGnN4On\",\"currentLocation\":\"vS4FeP0vZX\",\"geoLocation\":\"xHMF6ksbcB\",\"statusId\":{\"id\":0,\"statusName\":\"6INPAVVH58\"},\"userId\":{\"id\":0,\"username\":\"qIHEimgbdn\",\"password\":\"7jaxCmnlpR\",\"status\":\"t3oeMXQZHn\"}}"}, "url": {"raw": "http://localhost:9766/ContactDetails/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "save"]}}}, {"name": "Update ContactDetails", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"salutation\":\"QnD4l9s94P\",\"firstName\":\"CDjMEMWxBM\",\"lastName\":\"HzJguctEEf\",\"displayName\":\"6N0Ho1BeLU\",\"avatarImgPath\":\"EoIImIWczn\",\"phone\":\"u9S6LIZwOk\",\"emailId\":\"FMjhHEQDAz\",\"mobileNo\":\"vigYQ3sOwI\",\"trustPilotId\":\"fjXNZIItLw\",\"address\":\"JFM6RIydvO\",\"aboutMe\":\"9zA5bFxv86\",\"bioCoverLetter\":\"EogMUzChSz\",\"locateTutorBadge\":\"In4hwVYZ5i\",\"locateTutorSummary\":\"2gLBJPbYkf\",\"genderId\":\"3nawGiSVVC\",\"currentLocation\":\"VpNLnGDLcc\",\"geoLocation\":\"C1fCSLrucA\",\"statusId\":{\"id\":0,\"statusName\":\"1kgeJOIqDb\"},\"userId\":{\"id\":0,\"username\":\"ij8TGHUsh5\",\"password\":\"dxGPultnXT\",\"status\":\"rYXpN29KRJ\"}}"}, "url": {"raw": "http://localhost:9766/ContactDetails/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "update", "{value}"]}}}, {"name": "ReadById ContactDetails", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/ContactDetails/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "{value}"]}}}, {"name": "GetAll ContactDetails", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/ContactDetails/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "list"]}}}, {"name": "Dynamic Query ContactDetails", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter ContactDetails", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/ContactDetails/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "filter", "{key}", "{value}"]}}}, {"name": "Sort ContactDetails", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/ContactDetails/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial ContactDetails", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"salutation\":\"yjE0HduF8H\",\"firstName\":\"As6ykYn0MP\",\"lastName\":\"bd2F6Olm<PERSON>\",\"displayName\":\"4we7FFyond\",\"avatarImgPath\":\"0TYCEuEWz2\",\"phone\":\"YLWUVZyMgi\",\"emailId\":\"kLTwIsQCaH\",\"mobileNo\":\"bUDMSyVYb7\",\"trustPilotId\":\"wOyppl0LiK\",\"address\":\"H8kW95rWPk\",\"aboutMe\":\"zX6LpuKZHd\",\"bioCoverLetter\":\"zTA53VMX7j\",\"locateTutorBadge\":\"BdpCXGDB6o\",\"locateTutorSummary\":\"v4ojk9vJxk\",\"genderId\":\"ovAZfJVHcI\",\"currentLocation\":\"KYgSLxd0VO\",\"geoLocation\":\"dUMigWD7qB\",\"statusId\":{\"id\":0,\"statusName\":\"sgBirONyvD\"},\"userId\":{\"id\":0,\"username\":\"jQq0kufBHG\",\"password\":\"fs18fwSWBk\",\"status\":\"dgQXpgUYAY\"}}"}, "url": {"raw": "http://localhost:9766/ContactDetails/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "ContactDetails", "updatePartialEntity", "{id}"]}}}]}, {"name": "Coupon", "item": [{"name": "Save Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"couponCode\":\"4yj8UUKNgA\",\"creditPoints\":0,\"maxUsages\":0}"}, "url": {"raw": "http://localhost:9766/Coupon/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "save"]}}}, {"name": "Update Coupon", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"couponCode\":\"z8CBqQNtvS\",\"creditPoints\":0,\"maxUsages\":0}"}, "url": {"raw": "http://localhost:9766/Coupon/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "update", "{value}"]}}}, {"name": "ReadById Coupon", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Coupon/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "{value}"]}}}, {"name": "GetAll Coupon", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Coupon/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "list"]}}}, {"name": "Dynamic Query Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Coupon", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Coupon/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "filter", "{key}", "{value}"]}}}, {"name": "Sort Coupon", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Coupon/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"couponCode\":\"oypB8jxw3w\",\"creditPoints\":0,\"maxUsages\":0}"}, "url": {"raw": "http://localhost:9766/Coupon/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Coupon", "updatePartialEntity", "{id}"]}}}]}, {"name": "CreditPointsEarned", "item": [{"name": "Save CreditPointsEarned", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"J9U2gvJAP0\",\"firstName\":\"10PjfIsQ0s\",\"lastName\":\"bcPpOhpPB6\",\"displayName\":\"FhP3SOt827\",\"avatarImgPath\":\"WD1Tr4aQMY\",\"phone\":\"vkQotABkxv\",\"emailId\":\"95FU2Q8RSV\",\"mobileNo\":\"XFc8YRfbsm\",\"trustPilotId\":\"8dG3keqNmX\",\"address\":\"EyPPx8cQ8Z\",\"aboutMe\":\"VONrKkGBrD\",\"bioCoverLetter\":\"Zq2EpsddHd\",\"locateTutorBadge\":\"nFp2pthdjW\",\"locateTutorSummary\":\"MvuBWa98UD\",\"genderId\":\"ihoUWZhCbg\",\"currentLocation\":\"JNdkXP6Rh0\",\"geoLocation\":\"lXwj94hzkx\"},\"amount\":0,\"creditPointSource\":{\"id\":0,\"sourceKey\":\"l5vI13xkw2\",\"sourceDesc\":\"v0HHzWaD0n\"}}"}, "url": {"raw": "http://localhost:9766/CreditPointsEarned/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "save"]}}}, {"name": "Update CreditPointsEarned", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"v450qqiZHh\",\"firstName\":\"qkAH8YdVgt\",\"lastName\":\"3vS0edpa92\",\"displayName\":\"1xn5Cdi8gG\",\"avatarImgPath\":\"tc5h6W0GwP\",\"phone\":\"eLxBM2ywmY\",\"emailId\":\"QlvSTkKh1T\",\"mobileNo\":\"r4kUichAS2\",\"trustPilotId\":\"dYtL5fZfcr\",\"address\":\"StW4IsGfro\",\"aboutMe\":\"Gl6wwdXdxi\",\"bioCoverLetter\":\"QkfHpCTHCP\",\"locateTutorBadge\":\"ImmM0PxcmC\",\"locateTutorSummary\":\"LRSvUIJY0z\",\"genderId\":\"LPLjVvye2U\",\"currentLocation\":\"BOsJcapXOv\",\"geoLocation\":\"l9lGkKrrwk\"},\"amount\":0,\"creditPointSource\":{\"id\":0,\"sourceKey\":\"8s7YhF00IP\",\"sourceDesc\":\"9vgg0FJJAJ\"}}"}, "url": {"raw": "http://localhost:9766/CreditPointsEarned/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "update", "{value}"]}}}, {"name": "ReadById CreditPointsEarned", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/CreditPointsEarned/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "{value}"]}}}, {"name": "GetAll CreditPointsEarned", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/CreditPointsEarned/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "list"]}}}, {"name": "Dynamic Query CreditPointsEarned", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter CreditPointsEarned", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/CreditPointsEarned/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "filter", "{key}", "{value}"]}}}, {"name": "Sort CreditPointsEarned", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/CreditPointsEarned/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial CreditPointsEarned", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"pW68309mrU\",\"firstName\":\"TqiW7BSYin\",\"lastName\":\"cN9mxiHBdC\",\"displayName\":\"UGfI2FxzOX\",\"avatarImgPath\":\"rc4NWBeXJM\",\"phone\":\"y2pEYaajFf\",\"emailId\":\"CqrSYg3bFf\",\"mobileNo\":\"YEvfcztrsF\",\"trustPilotId\":\"eYgNnwFk2m\",\"address\":\"fFujRcMUh5\",\"aboutMe\":\"E0VFJKHL0K\",\"bioCoverLetter\":\"8ChOhNsDkg\",\"locateTutorBadge\":\"aaXqqMZfPv\",\"locateTutorSummary\":\"0DlszOhina\",\"genderId\":\"k1G1UZ0fhM\",\"currentLocation\":\"9jFN79vfUu\",\"geoLocation\":\"GTiPXECobV\"},\"amount\":0,\"creditPointSource\":{\"id\":0,\"sourceKey\":\"78xMvr4qrY\",\"sourceDesc\":\"51PgfWUnKj\"}}"}, "url": {"raw": "http://localhost:9766/CreditPointsEarned/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "CreditPointsEarned", "updatePartialEntity", "{id}"]}}}]}, {"name": "DefaultRolePermissionsMaster", "item": [{"name": "Save DefaultRolePermissionsMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":{\"id\":0,\"roleKey\":\"a1NgBAz9vG\",\"roleName\":\"c5Tm9cKdwX\",\"roleDesc\":\"t7tzP0UsNo\"},\"permissionKey\":{\"id\":0,\"permissionName\":\"hPIcgJr9au\",\"permissionKey\":\"wBjfvHQh2Q\",\"permissionDesc\":\"pbVo7jAnbq\"}}"}, "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "save"]}}}, {"name": "Update DefaultRolePermissionsMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":{\"id\":0,\"roleKey\":\"AKY7GMyx03\",\"roleName\":\"onVeRiTKy5\",\"roleDesc\":\"XVfRjqjKtq\"},\"permissionKey\":{\"id\":0,\"permissionName\":\"YuWUZTQCrA\",\"permissionKey\":\"CrhKu16dsa\",\"permissionDesc\":\"Iz8Tti3JHt\"}}"}, "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "update", "{value}"]}}}, {"name": "ReadById DefaultRolePermissionsMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "{value}"]}}}, {"name": "GetAll DefaultRolePermissionsMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "list"]}}}, {"name": "Dynamic Query DefaultRolePermissionsMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter DefaultRolePermissionsMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort DefaultRolePermissionsMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial DefaultRolePermissionsMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":{\"id\":0,\"roleKey\":\"r80b3ptsZQ\",\"roleName\":\"63oSM2FmRj\",\"roleDesc\":\"WNwgJwtSbw\"},\"permissionKey\":{\"id\":0,\"permissionName\":\"pbuGOb5yCx\",\"permissionKey\":\"z59RDsO4Nv\",\"permissionDesc\":\"lwhXY6tvQn\"}}"}, "url": {"raw": "http://localhost:9766/DefaultRolePermissionsMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "DefaultRolePermissionsMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "FieldTypeMaster", "item": [{"name": "Save FieldTypeMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"fieldTypeName\":\"Pe71LVh7C9\",\"fieldTypeDesc\":\"JtU5hf3cu8\"}"}, "url": {"raw": "http://localhost:9766/FieldTypeMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "save"]}}}, {"name": "Update FieldTypeMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"fieldTypeName\":\"ga5neIQNiC\",\"fieldTypeDesc\":\"2HFRphbVhX\"}"}, "url": {"raw": "http://localhost:9766/FieldTypeMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "update", "{value}"]}}}, {"name": "ReadById FieldTypeMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/FieldTypeMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "{value}"]}}}, {"name": "GetAll FieldTypeMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/FieldTypeMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "list"]}}}, {"name": "Dynamic Query FieldTypeMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter FieldTypeMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/FieldTypeMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort FieldTypeMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/FieldTypeMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial FieldTypeMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"fieldTypeName\":\"ADix1NSebh\",\"fieldTypeDesc\":\"XvsrwDUvaN\"}"}, "url": {"raw": "http://localhost:9766/FieldTypeMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "FieldTypeMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "MasterConfig", "item": [{"name": "Save MasterConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"fieldName\":\"4ASVVmx5QC\",\"fieldLabel\":\"s7qdUqix3t\",\"mappedDtoField\":\"kFP9AIxmZz\",\"maxLength\":0,\"minLength\":0,\"validationRegex\":\"rJxwxzRQqL\",\"optionLabel\":\"a1wEWB8VQZ\",\"getApiUrl\":\"NEwCKBuLUr\",\"displayPreference\":0,\"masterManager\":{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"qmyoT2SwjC\",\"displayName\":\"m934U5IPvw\",\"masterDesc\":\"8HyWTFjhUE\",\"getApiUrl\":\"brQkZLF6ht\",\"saveApiUrl\":\"QgGGxVMCt6\",\"updateApiUrl\":\"Vb40M2ARO0\",\"displayPreference\":0},\"fieldTypeMaster\":{\"id\":0,\"fieldTypeName\":\"9RW9TCzY8r\",\"fieldTypeDesc\":\"JgrqMs7MKC\"}}"}, "url": {"raw": "http://localhost:9766/MasterConfig/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "save"]}}}, {"name": "Update MasterConfig", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"fieldName\":\"ri9AbiWCRS\",\"fieldLabel\":\"4FvXFP39ZV\",\"mappedDtoField\":\"1Di53CUO7q\",\"maxLength\":0,\"minLength\":0,\"validationRegex\":\"ZOGa2mVFhv\",\"optionLabel\":\"bCUALZ9vZ5\",\"getApiUrl\":\"A8GMgTCyLn\",\"displayPreference\":0,\"masterManager\":{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"3DfN6KI3zQ\",\"displayName\":\"zOVtigLt6C\",\"masterDesc\":\"j0NmOGtigF\",\"getApiUrl\":\"F9RQL1TUcC\",\"saveApiUrl\":\"OTLGXwv21R\",\"updateApiUrl\":\"Y50L3l56LS\",\"displayPreference\":0},\"fieldTypeMaster\":{\"id\":0,\"fieldTypeName\":\"BcRlOFlvdo\",\"fieldTypeDesc\":\"ajxTrmLV2Q\"}}"}, "url": {"raw": "http://localhost:9766/MasterConfig/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "update", "{value}"]}}}, {"name": "ReadById MasterConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterConfig/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "{value}"]}}}, {"name": "GetAll MasterConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterConfig/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "list"]}}}, {"name": "Dynamic Query MasterConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter MasterConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterConfig/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "filter", "{key}", "{value}"]}}}, {"name": "Sort MasterConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterConfig/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial MasterConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"fieldName\":\"QK6ZMsuwvj\",\"fieldLabel\":\"5ACt47kXLJ\",\"mappedDtoField\":\"YMhZ2hT7kd\",\"maxLength\":0,\"minLength\":0,\"validationRegex\":\"by8K5hVary\",\"optionLabel\":\"vRqrQ98Yef\",\"getApiUrl\":\"NJBhghRVFd\",\"displayPreference\":0,\"masterManager\":{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"AP6Y2fWRQi\",\"displayName\":\"v1v0wTJy0l\",\"masterDesc\":\"FjDALpSYLG\",\"getApiUrl\":\"HvafzJTHGb\",\"saveApiUrl\":\"RcHus5heet\",\"updateApiUrl\":\"1PLUDtSRoc\",\"displayPreference\":0},\"fieldTypeMaster\":{\"id\":0,\"fieldTypeName\":\"axxBy4PXhp\",\"fieldTypeDesc\":\"JrOH96kJxT\"}}"}, "url": {"raw": "http://localhost:9766/MasterConfig/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "MasterConfig", "updatePartialEntity", "{id}"]}}}]}, {"name": "Master<PERSON><PERSON><PERSON>", "item": [{"name": "Save MasterManager", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"EAIyzcSDsl\",\"displayName\":\"uXbVhTnY1C\",\"masterDesc\":\"3QagJjZCPt\",\"getApiUrl\":\"A3g7Dqu7Ni\",\"saveApiUrl\":\"CywhhgNZeA\",\"updateApiUrl\":\"XIfAnDTZde\",\"displayPreference\":0}"}, "url": {"raw": "http://localhost:9766/MasterManager/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "save"]}}}, {"name": "Update Master<PERSON>ana<PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"mVmB3lpwR0\",\"displayName\":\"QVMKpFntel\",\"masterDesc\":\"2QmN2lnSaN\",\"getApiUrl\":\"Io7QjjmdJs\",\"saveApiUrl\":\"nH0vYgloe5\",\"updateApiUrl\":\"CGkt6MucAC\",\"displayPreference\":0}"}, "url": {"raw": "http://localhost:9766/MasterManager/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "update", "{value}"]}}}, {"name": "ReadById MasterManager", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterManager/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "{value}"]}}}, {"name": "GetAll MasterManager", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterManager/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "list"]}}}, {"name": "Dynamic Query MasterManager", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "<PERSON>lter <PERSON>anager", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterManager/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "filter", "{key}", "{value}"]}}}, {"name": "Sort MasterManager", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/MasterManager/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial MasterManager", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"serialVersionUID\":0,\"id\":0,\"masterName\":\"rCxHzTbiI9\",\"displayName\":\"9nGqAiIUWw\",\"masterDesc\":\"YL8gw7gUUy\",\"getApiUrl\":\"Lkcq5oXjFQ\",\"saveApiUrl\":\"OszhJQoE0o\",\"updateApiUrl\":\"RLCfWqBnCL\",\"displayPreference\":0}"}, "url": {"raw": "http://localhost:9766/MasterManager/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Master<PERSON><PERSON><PERSON>", "updatePartialEntity", "{id}"]}}}]}, {"name": "Payment", "item": [{"name": "Save Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"amount\":0,\"currencyCode\":\"c8JXYie5Xe\",\"paymentMethod\":\"PHAGf94yTE\",\"transactionId\":\"IAmMwPdrJy\",\"paymentStatus\":{\"id\":0,\"statusName\":\"vBwc1jPGMe\"}}"}, "url": {"raw": "http://localhost:9766/Payment/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "save"]}}}, {"name": "Update Payment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"amount\":0,\"currencyCode\":\"bpKT8ljscv\",\"paymentMethod\":\"xAa8aE9Y7u\",\"transactionId\":\"4SMMaJDMjT\",\"paymentStatus\":{\"id\":0,\"statusName\":\"HOnKomOGi9\"}}"}, "url": {"raw": "http://localhost:9766/Payment/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "update", "{value}"]}}}, {"name": "ReadById Payment", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Payment/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "{value}"]}}}, {"name": "GetAll Payment", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Payment/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "list"]}}}, {"name": "Dynamic Query Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Payment", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Payment/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "filter", "{key}", "{value}"]}}}, {"name": "Sort Payment", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Payment/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"amount\":0,\"currencyCode\":\"ROxlZUQPRx\",\"paymentMethod\":\"CTdMeO90i9\",\"transactionId\":\"etpUERPrF6\",\"paymentStatus\":{\"id\":0,\"statusName\":\"V9MSw9vFQs\"}}"}, "url": {"raw": "http://localhost:9766/Payment/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Payment", "updatePartialEntity", "{id}"]}}}]}, {"name": "PermissionGroupType", "item": [{"name": "Save PermissionGroupType", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"groupTypeKey\":\"3BWXNVe5oT\",\"groupTypeName\":\"VwtBwuKCP9\",\"groupTypeDesc\":\"uWPo3NVkBi\"}"}, "url": {"raw": "http://localhost:9766/PermissionGroupType/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "save"]}}}, {"name": "Update PermissionGroupType", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"groupTypeKey\":\"lCfcp3q9zH\",\"groupTypeName\":\"yLwIbv64vF\",\"groupTypeDesc\":\"lmJlnWL6o7\"}"}, "url": {"raw": "http://localhost:9766/PermissionGroupType/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "update", "{value}"]}}}, {"name": "ReadById PermissionGroupType", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionGroupType/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "{value}"]}}}, {"name": "GetAll PermissionGroupType", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionGroupType/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "list"]}}}, {"name": "Dynamic Query PermissionGroupType", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter PermissionGroupType", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionGroupType/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "filter", "{key}", "{value}"]}}}, {"name": "Sort PermissionGroupType", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionGroupType/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial PermissionGroupType", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"groupTypeKey\":\"INce77QsAx\",\"groupTypeName\":\"4u8MGpq1MG\",\"groupTypeDesc\":\"CYoKg43D3U\"}"}, "url": {"raw": "http://localhost:9766/PermissionGroupType/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionGroupType", "updatePartialEntity", "{id}"]}}}]}, {"name": "PermissionMaster", "item": [{"name": "Save PermissionMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"permissionName\":\"bU62a4Wikd\",\"permissionKey\":\"0I2UJt4GKQ\",\"permissionDesc\":\"OXBDIE7po9\",\"groupTypeKey\":{\"id\":0,\"groupTypeKey\":\"jHT5yLfsad\",\"groupTypeName\":\"hnTflwZIkJ\",\"groupTypeDesc\":\"agEHS44HAR\"}}"}, "url": {"raw": "http://localhost:9766/PermissionMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "save"]}}}, {"name": "Update PermissionMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"permissionName\":\"EX8YjHsAA6\",\"permissionKey\":\"is23UGxnpt\",\"permissionDesc\":\"LC411jTbs0\",\"groupTypeKey\":{\"id\":0,\"groupTypeKey\":\"yuQF174GPi\",\"groupTypeName\":\"L5unkKH8yg\",\"groupTypeDesc\":\"4q1rEaDxbB\"}}"}, "url": {"raw": "http://localhost:9766/PermissionMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "update", "{value}"]}}}, {"name": "ReadById PermissionMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "{value}"]}}}, {"name": "GetAll PermissionMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "list"]}}}, {"name": "Dynamic Query PermissionMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter PermissionMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort PermissionMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/PermissionMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial PermissionMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"permissionName\":\"otbMMwejZR\",\"permissionKey\":\"FJQOoUSO2m\",\"permissionDesc\":\"RaDXaonJ1k\",\"groupTypeKey\":{\"id\":0,\"groupTypeKey\":\"oK40NvFc1L\",\"groupTypeName\":\"vNaI5SRlAi\",\"groupTypeDesc\":\"zYBq0zrDoo\"}}"}, "url": {"raw": "http://localhost:9766/PermissionMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "PermissionMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "QueryForMaster", "item": [{"name": "Save QueryForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"queryForName\":\"hxO8QcnKRP\",\"queryForDesc\":\"oisPTxkuEt\"}"}, "url": {"raw": "http://localhost:9766/QueryForMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "save"]}}}, {"name": "Update QueryForMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"queryForName\":\"gOHWr38ZAG\",\"queryForDesc\":\"KFnWgexyy9\"}"}, "url": {"raw": "http://localhost:9766/QueryForMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "update", "{value}"]}}}, {"name": "ReadById QueryForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryForMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "{value}"]}}}, {"name": "GetAll QueryForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryForMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "list"]}}}, {"name": "Dynamic Query QueryForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter QueryForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryForMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort QueryForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryForMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial QueryForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"queryForName\":\"Z8U5lTBv4X\",\"queryForDesc\":\"gsAoo76b0H\"}"}, "url": {"raw": "http://localhost:9766/QueryForMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryForMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "QueryMaster", "item": [{"name": "Save QueryMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"query\":\"JZlNZv7jlk\",\"queryName\":\"frKoIZlEWl\",\"queryForId\":{\"id\":0,\"queryForName\":\"EJeaVVDbOm\",\"queryForDesc\":\"eZN5rf3KvU\"}}"}, "url": {"raw": "http://localhost:9766/QueryMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "save"]}}}, {"name": "Update QueryMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"query\":\"SeM9GsCvYy\",\"queryName\":\"rEpKyzA3jv\",\"queryForId\":{\"id\":0,\"queryForName\":\"xx2RoyyPW8\",\"queryForDesc\":\"XR9Uaox5yP\"}}"}, "url": {"raw": "http://localhost:9766/QueryMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "update", "{value}"]}}}, {"name": "ReadById QueryMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "{value}"]}}}, {"name": "GetAll QueryMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "list"]}}}, {"name": "Dynamic Query QueryMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter QueryMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort QueryMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/QueryMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial QueryMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"query\":\"f7uAlRhxno\",\"queryName\":\"ioXs7zECBx\",\"queryForId\":{\"id\":0,\"queryForName\":\"L6Aw8shr5U\",\"queryForDesc\":\"FQ02qHqcgu\"}}"}, "url": {"raw": "http://localhost:9766/QueryMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "QueryMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "RoleMaster", "item": [{"name": "Save RoleMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":\"jKMokNCCF6\",\"roleName\":\"ZmMigTD9WW\",\"roleDesc\":\"MiKuh2c6lZ\"}"}, "url": {"raw": "http://localhost:9766/RoleMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "save"]}}}, {"name": "Update RoleMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":\"68IOCDYYg0\",\"roleName\":\"G3OxHGhz7q\",\"roleDesc\":\"Al33mt653D\"}"}, "url": {"raw": "http://localhost:9766/RoleMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "update", "{value}"]}}}, {"name": "ReadById RoleMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/RoleMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "{value}"]}}}, {"name": "GetAll RoleMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/RoleMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "list"]}}}, {"name": "Dynamic Query RoleMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Role<PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/RoleMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort RoleMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/RoleMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial RoleMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"roleKey\":\"fYknX6K0YT\",\"roleName\":\"BpLSLoggdm\",\"roleDesc\":\"NM9pZCJxlK\"}"}, "url": {"raw": "http://localhost:9766/RoleMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "RoleMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "Session", "item": [{"name": "Save Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"WvKo1Seil9\",\"firstName\":\"hKYpNFwRFl\",\"lastName\":\"8I9HpEukfA\",\"displayName\":\"Qx4vITHJY7\",\"avatarImgPath\":\"1zI9KbVf3H\",\"phone\":\"s2Bh2pIBdV\",\"emailId\":\"iEmG6vtgE2\",\"mobileNo\":\"zrT5ulLlRU\",\"trustPilotId\":\"X14VKneWeY\",\"address\":\"lPNLIaoBdl\",\"aboutMe\":\"whhTuMn4K7\",\"bioCoverLetter\":\"LjNQ85hh6l\",\"locateTutorBadge\":\"zmY93nVgdb\",\"locateTutorSummary\":\"B2jhAMiSvc\",\"genderId\":\"HRF5dWpnPQ\",\"currentLocation\":\"Tyikne1OqT\",\"geoLocation\":\"QxTurV0bBm\"},\"sessionType\":{\"id\":0,\"sessionTypeKey\":\"4rtx36lyEe\",\"sessionTypeDesc\":\"bPDxmxisHj\"},\"title\":\"Ki0OBiT3f2\",\"subject\":{\"id\":0,\"subjectKey\":\"qpZG8pRCn3\",\"subjectShortName\":\"Y7LqpRi7ey\",\"subjectDescription\":\"nJGgKXsT7L\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"4uEgQrZHHe\",\"subCategoryDesc\":\"J3iOagYdvv\"},\"description\":\"mJTJHU6nbQ\",\"sessionPrice\":0,\"currencyCode\":\"na34qZypEm\",\"minStudents\":0,\"maxStudents\":0,\"sessionLocation\":{\"id\":0,\"locationKey\":\"W4hGPngSJ2\",\"locationValue\":\"AjBMC9nfeN\"}}"}, "url": {"raw": "http://localhost:9766/Session/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "save"]}}}, {"name": "Update Session", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"rTfNhoLiA6\",\"firstName\":\"tXbvv06h4s\",\"lastName\":\"a5sWRq7exP\",\"displayName\":\"zhHwzL6Hu9\",\"avatarImgPath\":\"t6oAzmzf0V\",\"phone\":\"j1MuX7zJe8\",\"emailId\":\"KemFfwmCN8\",\"mobileNo\":\"N2Ou6mejW0\",\"trustPilotId\":\"Zr0K6OHQfh\",\"address\":\"iBRd0uluFG\",\"aboutMe\":\"6mN8Ash2uY\",\"bioCoverLetter\":\"b5HawticGD\",\"locateTutorBadge\":\"wqGPhSZ7kQ\",\"locateTutorSummary\":\"hcTRtmjqFB\",\"genderId\":\"2Dd6VInHWd\",\"currentLocation\":\"3HOGoUr5BS\",\"geoLocation\":\"UhiSmzvOnz\"},\"sessionType\":{\"id\":0,\"sessionTypeKey\":\"cVJKnGeBDD\",\"sessionTypeDesc\":\"o6ckm27Ulp\"},\"title\":\"hg5IqxnjBk\",\"subject\":{\"id\":0,\"subjectKey\":\"sKAnHYt576\",\"subjectShortName\":\"NkKS9zvdh1\",\"subjectDescription\":\"JvllS1qG9O\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"KqHqkJLuma\",\"subCategoryDesc\":\"IJaEuLoV25\"},\"description\":\"LmeG0bm36M\",\"sessionPrice\":0,\"currencyCode\":\"iRLt9cfrWn\",\"minStudents\":0,\"maxStudents\":0,\"sessionLocation\":{\"id\":0,\"locationKey\":\"CxinbF24Rq\",\"locationValue\":\"HHAAei0zbJ\"}}"}, "url": {"raw": "http://localhost:9766/Session/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "update", "{value}"]}}}, {"name": "ReadById Session", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Session/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "{value}"]}}}, {"name": "GetAll Session", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Session/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "list"]}}}, {"name": "Dynamic Query Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Session", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Session/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "filter", "{key}", "{value}"]}}}, {"name": "Sort Session", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Session/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"T8waJcriHk\",\"firstName\":\"pQfy8KLnIk\",\"lastName\":\"Ow1Lj6q5WU\",\"displayName\":\"GXn5HRGYbT\",\"avatarImgPath\":\"b4BaBGJcav\",\"phone\":\"jsVQPwUSfu\",\"emailId\":\"sAQuWDuBNf\",\"mobileNo\":\"b3tPmuszhU\",\"trustPilotId\":\"sYeoPQumR9\",\"address\":\"3Kvo8Zrats\",\"aboutMe\":\"SscxinsIm2\",\"bioCoverLetter\":\"yR29EOAgur\",\"locateTutorBadge\":\"PUErlj2vMx\",\"locateTutorSummary\":\"uiUwGF1PLk\",\"genderId\":\"xmCVEMig5K\",\"currentLocation\":\"Na7JYcacWV\",\"geoLocation\":\"T0LulWlM9U\"},\"sessionType\":{\"id\":0,\"sessionTypeKey\":\"7fju5ev1v6\",\"sessionTypeDesc\":\"kFukSa0vZC\"},\"title\":\"z44VLSIily\",\"subject\":{\"id\":0,\"subjectKey\":\"MK9AbsJk9D\",\"subjectShortName\":\"Q53L9rcn7d\",\"subjectDescription\":\"93hjhZwio2\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"rkSUZYHiu1\",\"subCategoryDesc\":\"XclFyJGeor\"},\"description\":\"qCQvsjWo9d\",\"sessionPrice\":0,\"currencyCode\":\"9946eMXoq3\",\"minStudents\":0,\"maxStudents\":0,\"sessionLocation\":{\"id\":0,\"locationKey\":\"HN6QshHUWP\",\"locationValue\":\"OZZiDrEl7E\"}}"}, "url": {"raw": "http://localhost:9766/Session/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Session", "updatePartialEntity", "{id}"]}}}]}, {"name": "SessionEnquiry", "item": [{"name": "Save SessionEnquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"Tv2iUblhFJ\",\"firstName\":\"Lm3RCKTkxt\",\"lastName\":\"SmlW38HsZl\",\"displayName\":\"Kh1JaV6NfB\",\"avatarImgPath\":\"PzKRDQLjAn\",\"phone\":\"tVZUPHb9Nj\",\"emailId\":\"ULNf4hDoxW\",\"mobileNo\":\"hfg9n8mDns\",\"trustPilotId\":\"N9MlNgOkq9\",\"address\":\"zwsWU7S1MD\",\"aboutMe\":\"cNSz61vvky\",\"bioCoverLetter\":\"zjy6NQAzkY\",\"locateTutorBadge\":\"DxDfMLrBVH\",\"locateTutorSummary\":\"D6zuYcPNSw\",\"genderId\":\"zcWMyyOW50\",\"currentLocation\":\"xWdgkA5BjI\",\"geoLocation\":\"0IXdy8NWsk\"},\"tutor\":{\"id\":0,\"salutation\":\"indr8bGWBY\",\"firstName\":\"m3ryAvHJs1\",\"lastName\":\"3HIj7qR2NQ\",\"displayName\":\"bcjRI0j9dv\",\"avatarImgPath\":\"nHMOrnllEe\",\"phone\":\"Mp9Ittb2tU\",\"emailId\":\"C4ATvgSAxx\",\"mobileNo\":\"zPf68MGdBJ\",\"trustPilotId\":\"ne6UMNkvBL\",\"address\":\"ubCTLGIVCK\",\"aboutMe\":\"DQa5pqWkBK\",\"bioCoverLetter\":\"FUglPUJIRj\",\"locateTutorBadge\":\"QrxMbFLoDT\",\"locateTutorSummary\":\"YxrLgtxXVH\",\"genderId\":\"GLqlX2bz9F\",\"currentLocation\":\"IzTplQvKcf\",\"geoLocation\":\"QfdispHln1\"},\"subject\":{\"id\":0,\"subjectKey\":\"t9VRbujoyG\",\"subjectShortName\":\"CvoCeWl7aa\",\"subjectDescription\":\"1KXPDeJRv0\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"vfwvPc6l0x\",\"subCategoryDesc\":\"6o9q5HCLLm\"},\"durationMin\":0,\"enquiryStatus\":\"YCtg75QX0Z\",\"subjectDescription\":\"RnPcBC2tKQ\",\"tutorNotes\":\"RYSWaUkxL4\"}"}, "url": {"raw": "http://localhost:9766/SessionEnquiry/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "save"]}}}, {"name": "Update SessionEnquiry", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"3dRTkWTHwJ\",\"firstName\":\"fVGFPA8Adz\",\"lastName\":\"DntKkHRBy0\",\"displayName\":\"YxbwbHj1D8\",\"avatarImgPath\":\"D7A0zMpQTZ\",\"phone\":\"UCUgm41D7H\",\"emailId\":\"aLo4pNSDkO\",\"mobileNo\":\"lRXfIL8elR\",\"trustPilotId\":\"dpVksCK8l5\",\"address\":\"KzPeEFwaL1\",\"aboutMe\":\"QbwmNkMil6\",\"bioCoverLetter\":\"9ECnJhqcOA\",\"locateTutorBadge\":\"NW4czbwPen\",\"locateTutorSummary\":\"kvsZLRwiWW\",\"genderId\":\"8roNoZYsVg\",\"currentLocation\":\"6vC36nITDd\",\"geoLocation\":\"nzFhkhf8US\"},\"tutor\":{\"id\":0,\"salutation\":\"cidPcpiC8b\",\"firstName\":\"CN62htJgRI\",\"lastName\":\"WwDQe3WSBL\",\"displayName\":\"fKIrCCKA3C\",\"avatarImgPath\":\"EpdBmMOWCh\",\"phone\":\"bKVXSDPwIL\",\"emailId\":\"LHgf1YN8Px\",\"mobileNo\":\"cz7rsaYDk9\",\"trustPilotId\":\"PLLXxdeWBJ\",\"address\":\"6KPy0esYrA\",\"aboutMe\":\"4b3am0Qmrl\",\"bioCoverLetter\":\"FWuYbCAAtp\",\"locateTutorBadge\":\"IE1SmXNa4u\",\"locateTutorSummary\":\"MrXSseWrBR\",\"genderId\":\"Ca0jk0TYmq\",\"currentLocation\":\"gF7iyDyE9R\",\"geoLocation\":\"y2oYhSOgcy\"},\"subject\":{\"id\":0,\"subjectKey\":\"qifrGxbI0K\",\"subjectShortName\":\"XOHTByNF0s\",\"subjectDescription\":\"xNy2bcxdvc\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"RvVN8CpgJw\",\"subCategoryDesc\":\"jvqfrYL4V1\"},\"durationMin\":0,\"enquiryStatus\":\"wYj2X9M1OF\",\"subjectDescription\":\"1eckiNMxxx\",\"tutorNotes\":\"hGeHDVzQfs\"}"}, "url": {"raw": "http://localhost:9766/SessionEnquiry/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "update", "{value}"]}}}, {"name": "ReadById SessionEnquiry", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionEnquiry/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "{value}"]}}}, {"name": "GetAll SessionEnquiry", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionEnquiry/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "list"]}}}, {"name": "Dynamic Query SessionEnquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter <PERSON>Enquiry", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionEnquiry/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionEnquiry", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionEnquiry/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionEnquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"PsbmdW0wNC\",\"firstName\":\"dY5ig7wOF7\",\"lastName\":\"ZPLdweRhiH\",\"displayName\":\"9QS4M9eTNW\",\"avatarImgPath\":\"6Q6Fo7hLeB\",\"phone\":\"LNDxjOX1Pv\",\"emailId\":\"3qZPGCvKbX\",\"mobileNo\":\"JJ6xeLzqkM\",\"trustPilotId\":\"JPj0lWr4de\",\"address\":\"OZKD8SMIWZ\",\"aboutMe\":\"mNohNflGTw\",\"bioCoverLetter\":\"TdfJkR3hGn\",\"locateTutorBadge\":\"e5TOT246DV\",\"locateTutorSummary\":\"sicMOkWivT\",\"genderId\":\"TbJ6idGKBg\",\"currentLocation\":\"baf8h5nDjI\",\"geoLocation\":\"hcVnIXvBOD\"},\"tutor\":{\"id\":0,\"salutation\":\"xxwz8vM1pk\",\"firstName\":\"5iuH1pig70\",\"lastName\":\"fiILRqzX5C\",\"displayName\":\"qvCDCuvlkH\",\"avatarImgPath\":\"RjIILOcvE8\",\"phone\":\"6QtbsdEWqm\",\"emailId\":\"Ysltpzgvyy\",\"mobileNo\":\"S5Ep4dwowK\",\"trustPilotId\":\"3USCEThJNi\",\"address\":\"GXZvDlZTEh\",\"aboutMe\":\"x0fIxO8NJM\",\"bioCoverLetter\":\"UBwLPe8zah\",\"locateTutorBadge\":\"668P8ehCt1\",\"locateTutorSummary\":\"HMBKEWl2hx\",\"genderId\":\"brQQQvRPE4\",\"currentLocation\":\"fpLzl84C2p\",\"geoLocation\":\"rdOEybRo2Z\"},\"subject\":{\"id\":0,\"subjectKey\":\"Ra0Eez9RK4\",\"subjectShortName\":\"hsiUMj8vdt\",\"subjectDescription\":\"FD4SJA53T3\"},\"subjectSubCategory\":{\"id\":0,\"subCategoryName\":\"xnDM9MjSog\",\"subCategoryDesc\":\"7yYd1e7PaO\"},\"durationMin\":0,\"enquiryStatus\":\"vzoFfsutKj\",\"subjectDescription\":\"LzwmVuypVl\",\"tutorNotes\":\"tpYTG19Hqi\"}"}, "url": {"raw": "http://localhost:9766/SessionEnquiry/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionEnquiry", "updatePartialEntity", "{id}"]}}}]}, {"name": "SessionLocation", "item": [{"name": "Save SessionLocation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"locationKey\":\"w7cAhYRJfQ\",\"locationValue\":\"qr8vFtjuZf\"}"}, "url": {"raw": "http://localhost:9766/SessionLocation/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "save"]}}}, {"name": "Update SessionLocation", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"locationKey\":\"QgwHXMOV7n\",\"locationValue\":\"9KxBns0ltQ\"}"}, "url": {"raw": "http://localhost:9766/SessionLocation/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "update", "{value}"]}}}, {"name": "ReadById SessionLocation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionLocation/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "{value}"]}}}, {"name": "GetAll SessionLocation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionLocation/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "list"]}}}, {"name": "Dynamic Query SessionLocation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SessionLocation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionLocation/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionLocation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionLocation/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionLocation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"locationKey\":\"2A8HzNayqj\",\"locationValue\":\"ByZmzZ9aT0\"}"}, "url": {"raw": "http://localhost:9766/SessionLocation/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionLocation", "updatePartialEntity", "{id}"]}}}]}, {"name": "SessionRecurrencePattern", "item": [{"name": "Save SessionRecurrencePattern", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"frequency\":\"oyZWjUhBoe\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}"}, "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "save"]}}}, {"name": "Update SessionRecurrencePattern", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"frequency\":\"IZ7SzULMSg\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}"}, "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "update", "{value}"]}}}, {"name": "ReadById SessionRecurrencePattern", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "{value}"]}}}, {"name": "GetAll SessionRecurrencePattern", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "list"]}}}, {"name": "Dynamic Query SessionRecurrencePattern", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter SessionRecurrencePattern", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "filter", "{key}", "{value}"]}}}, {"name": "Sort SessionRecurrencePattern", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial SessionRecurrencePattern", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"frequency\":\"ehXzbTbE81\",\"interval\":0,\"dayOfWeek\":0,\"weekOfMonth\":0,\"dayOfMonth\":0,\"occurrenceCount\":0}"}, "url": {"raw": "http://localhost:9766/SessionRecurrencePattern/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "SessionRecurrencePattern", "updatePartialEntity", "{id}"]}}}]}, {"name": "Settlement", "item": [{"name": "Save Settlement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"vreDNi8t1J\",\"firstName\":\"y7av57ABkq\",\"lastName\":\"PX6isReGlh\",\"displayName\":\"67IHnBDtRi\",\"avatarImgPath\":\"CQdQNEvYDn\",\"phone\":\"aOOJJjFh9K\",\"emailId\":\"1hb3PbBVan\",\"mobileNo\":\"34em8RVcJf\",\"trustPilotId\":\"oXP5tNWgnk\",\"address\":\"heeIzmajAF\",\"aboutMe\":\"Dg1FdF7Oqv\",\"bioCoverLetter\":\"jFxycAqKYB\",\"locateTutorBadge\":\"36BoJaF0oo\",\"locateTutorSummary\":\"vRc3YSRa5q\",\"genderId\":\"Q63C4gFSSx\",\"currentLocation\":\"p4sULqtYGN\",\"geoLocation\":\"AVTKNNHydI\"},\"totalEarnings\":0,\"platformFee\":0,\"netAmount\":0,\"paymentStatus\":\"D5KM2uBUrB\",\"transactionId\":\"5XRQmEn5zA\"}"}, "url": {"raw": "http://localhost:9766/Settlement/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "save"]}}}, {"name": "Update Settlement", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"qe9366gYya\",\"firstName\":\"LcFs4DH8YF\",\"lastName\":\"YNM1wTTtHC\",\"displayName\":\"LvhQhA32Za\",\"avatarImgPath\":\"g9B1MHGch9\",\"phone\":\"61l1AWFFjF\",\"emailId\":\"0Z5NLXcxVu\",\"mobileNo\":\"5GejgEmjgx\",\"trustPilotId\":\"3Jwaps3cCJ\",\"address\":\"awCaMHlM05\",\"aboutMe\":\"NwqFDiUHvc\",\"bioCoverLetter\":\"WRQYtKYWv4\",\"locateTutorBadge\":\"hNNjQKZ65A\",\"locateTutorSummary\":\"ikVWd6n3tY\",\"genderId\":\"puuSHr9jOd\",\"currentLocation\":\"W3IETIzby4\",\"geoLocation\":\"vWOipJxOho\"},\"totalEarnings\":0,\"platformFee\":0,\"netAmount\":0,\"paymentStatus\":\"7Wm0vBkGbr\",\"transactionId\":\"WQqbudPHsN\"}"}, "url": {"raw": "http://localhost:9766/Settlement/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "update", "{value}"]}}}, {"name": "ReadById Settlement", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Settlement/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "{value}"]}}}, {"name": "GetAll Settlement", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Settlement/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "list"]}}}, {"name": "Dynamic Query Settlement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter Settlement", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Settlement/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "filter", "{key}", "{value}"]}}}, {"name": "Sort Settlement", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Settlement/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Settlement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"DWUM7F3pn7\",\"firstName\":\"Z7pWnq3wKg\",\"lastName\":\"yapxQS74Fn\",\"displayName\":\"AesrW8igr3\",\"avatarImgPath\":\"qAf3j9M7vv\",\"phone\":\"rOwihnO8Xx\",\"emailId\":\"ZC6niDk12q\",\"mobileNo\":\"bvVz0doi6H\",\"trustPilotId\":\"XziosgMJ2Z\",\"address\":\"5MNglGD9fE\",\"aboutMe\":\"3iJL7DOusW\",\"bioCoverLetter\":\"vjEaHMgH7c\",\"locateTutorBadge\":\"cGT0cl3wce\",\"locateTutorSummary\":\"d3jplzM582\",\"genderId\":\"PoWbtRR7p8\",\"currentLocation\":\"YypT29R1xu\",\"geoLocation\":\"1ugHz6IXcb\"},\"totalEarnings\":0,\"platformFee\":0,\"netAmount\":0,\"paymentStatus\":\"os7i16Lr7y\",\"transactionId\":\"OirH4U9BbX\"}"}, "url": {"raw": "http://localhost:9766/Settlement/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Settlement", "updatePartialEntity", "{id}"]}}}]}, {"name": "Skill", "item": [{"name": "Save Skill", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"skillName\":\"BGbLPXoDAm\"}"}, "url": {"raw": "http://localhost:9766/Skill/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "save"]}}}, {"name": "Update Skill", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"skillName\":\"l3BBlUI8RU\"}"}, "url": {"raw": "http://localhost:9766/Skill/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "update", "{value}"]}}}, {"name": "ReadById <PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Skill/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "{value}"]}}}, {"name": "GetAll Skill", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Skill/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "list"]}}}, {"name": "Dynamic Query Skill", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Skill/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "filter", "{key}", "{value}"]}}}, {"name": "<PERSON><PERSON>ll", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/Skill/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial Skill", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"skillName\":\"KmjqGbRDOI\"}"}, "url": {"raw": "http://localhost:9766/Skill/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "Skill", "updatePartialEntity", "{id}"]}}}]}, {"name": "StatusForMaster", "item": [{"name": "Save StatusForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusForName\":\"xvXr1M5IqI\",\"statusForDesc\":\"wWRDSqCzWA\"}"}, "url": {"raw": "http://localhost:9766/StatusForMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "save"]}}}, {"name": "Update StatusForMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusForName\":\"fSFG3uKfU7\",\"statusForDesc\":\"X67GG21Nhl\"}"}, "url": {"raw": "http://localhost:9766/StatusForMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "update", "{value}"]}}}, {"name": "ReadById StatusForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusForMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "{value}"]}}}, {"name": "GetAll StatusForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusForMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "list"]}}}, {"name": "Dynamic Query StatusForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter StatusForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusForMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort StatusForMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusForMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial StatusForMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusForName\":\"XQowAlBend\",\"statusForDesc\":\"EuiYRQr6kk\"}"}, "url": {"raw": "http://localhost:9766/StatusForMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusForMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "StatusMaster", "item": [{"name": "Save StatusMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusName\":\"1oP2tvmSzz\",\"statusForId\":{\"id\":0,\"statusForName\":\"WVi6HiOsuU\",\"statusForDesc\":\"jN07TUuINs\"}}"}, "url": {"raw": "http://localhost:9766/StatusMaster/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "save"]}}}, {"name": "Update StatusMaster", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusName\":\"VRNiyMYIRx\",\"statusForId\":{\"id\":0,\"statusForName\":\"759eQkofPB\",\"statusForDesc\":\"S41QhhU8NZ\"}}"}, "url": {"raw": "http://localhost:9766/StatusMaster/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "update", "{value}"]}}}, {"name": "ReadById StatusMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusMaster/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "{value}"]}}}, {"name": "GetAll StatusMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusMaster/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "list"]}}}, {"name": "Dynamic Query StatusMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter StatusMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusMaster/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "filter", "{key}", "{value}"]}}}, {"name": "Sort StatusMaster", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StatusMaster/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial StatusMaster", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"statusName\":\"Zzsz9JSwNd\",\"statusForId\":{\"id\":0,\"statusForName\":\"h63gzsoYGd\",\"statusForDesc\":\"RGlEflVkn3\"}}"}, "url": {"raw": "http://localhost:9766/StatusMaster/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StatusMaster", "updatePartialEntity", "{id}"]}}}]}, {"name": "StudentFavoriteTutor", "item": [{"name": "Save StudentFavoriteTutor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"FbtMlXpi0c\",\"firstName\":\"rOjf58u5zs\",\"lastName\":\"7zM2Qoke5e\",\"displayName\":\"tArrQLWMl2\",\"avatarImgPath\":\"NXW0VSISie\",\"phone\":\"TuPLxttjRU\",\"emailId\":\"608wqQqMDF\",\"mobileNo\":\"U5lnnXWGC6\",\"trustPilotId\":\"uAMQU2gLi9\",\"address\":\"MTCWR19PwU\",\"aboutMe\":\"2mpICt1Zli\",\"bioCoverLetter\":\"jbiNNEcU11\",\"locateTutorBadge\":\"UvSfTYZKla\",\"locateTutorSummary\":\"0sYywEsrnO\",\"genderId\":\"Df8phlbyoR\",\"currentLocation\":\"IpGCXnKsmo\",\"geoLocation\":\"LlRh25fYSh\"},\"tutor\":{\"id\":0,\"salutation\":\"HGq3150uIU\",\"firstName\":\"Scs0fTSd7k\",\"lastName\":\"Zd6GXouy2T\",\"displayName\":\"5G5JjVrIuf\",\"avatarImgPath\":\"5RgPZL1e51\",\"phone\":\"Dc4e90cmm0\",\"emailId\":\"U0vzX9fMW3\",\"mobileNo\":\"EsGNVyszbE\",\"trustPilotId\":\"STK6dGlBVo\",\"address\":\"uUBDKEmeuG\",\"aboutMe\":\"Ss0tj5s4se\",\"bioCoverLetter\":\"npnMAvs5sr\",\"locateTutorBadge\":\"ecEBOdzWe1\",\"locateTutorSummary\":\"8nLvBKdkc7\",\"genderId\":\"6pjveyyK6p\",\"currentLocation\":\"0A7mtRMlBN\",\"geoLocation\":\"vXphgahpA1\"}}"}, "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "save"]}}}, {"name": "Update StudentFavoriteTutor", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"cR6NmkBG0s\",\"firstName\":\"136kWcQXUE\",\"lastName\":\"bfQay9ot9o\",\"displayName\":\"26SNa79PGH\",\"avatarImgPath\":\"3ya3Un36C2\",\"phone\":\"ER0jbzXabp\",\"emailId\":\"4GaCbzb61S\",\"mobileNo\":\"IZAI2FWAcn\",\"trustPilotId\":\"YzfJazkncy\",\"address\":\"JBaZLMaj9v\",\"aboutMe\":\"xsH8PLTI3f\",\"bioCoverLetter\":\"bYiDfLGXa0\",\"locateTutorBadge\":\"4kwUsILuRI\",\"locateTutorSummary\":\"j53e4PUtAn\",\"genderId\":\"AygY0xVrDS\",\"currentLocation\":\"8I6bVDnW63\",\"geoLocation\":\"IJ<PERSON>BCVXBK\"},\"tutor\":{\"id\":0,\"salutation\":\"Fy9hFRSfrO\",\"firstName\":\"u3RgGJ4K9u\",\"lastName\":\"4b8bUHMiUK\",\"displayName\":\"0ct5epixBg\",\"avatarImgPath\":\"hnxKCxpt4E\",\"phone\":\"bYRkCAiagu\",\"emailId\":\"LCB0Sxabze\",\"mobileNo\":\"AHEgrP9s9l\",\"trustPilotId\":\"IyHPtiKtti\",\"address\":\"rXpdd5sDfO\",\"aboutMe\":\"RJAmoTyeGe\",\"bioCoverLetter\":\"mL5vG7JDTH\",\"locateTutorBadge\":\"pW7myTujSB\",\"locateTutorSummary\":\"JSprnCtp5n\",\"genderId\":\"oxduKkOio6\",\"currentLocation\":\"FmNVYe2qqn\",\"geoLocation\":\"yZHJwCPdqc\"}}"}, "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "update", "{value}"]}}}, {"name": "ReadById StudentFavoriteTutor", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "{value}"]}}}, {"name": "GetAll StudentFavoriteTutor", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "list"]}}}, {"name": "Dynamic Query StudentFavoriteTutor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter StudentFavoriteTutor", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "filter", "{key}", "{value}"]}}}, {"name": "Sort StudentFavoriteTutor", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial StudentFavoriteTutor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"student\":{\"id\":0,\"salutation\":\"sZIlfs0yJe\",\"firstName\":\"r2kK5JII5n\",\"lastName\":\"ggUmNAebzZ\",\"displayName\":\"Do6hoByAu8\",\"avatarImgPath\":\"ZoP4Cz8115\",\"phone\":\"QEBkceRwJ6\",\"emailId\":\"ZUaQcfTQar\",\"mobileNo\":\"gVCHbvnVzK\",\"trustPilotId\":\"Jlqy5phw1v\",\"address\":\"R5E7E1CFov\",\"aboutMe\":\"VwSrkBVSmN\",\"bioCoverLetter\":\"S6MV5xHvug\",\"locateTutorBadge\":\"hVrgKORxCL\",\"locateTutorSummary\":\"FsL0hnx32u\",\"genderId\":\"JSvqkhSPrW\",\"currentLocation\":\"PMXqrQEwOR\",\"geoLocation\":\"yoHd2wfYID\"},\"tutor\":{\"id\":0,\"salutation\":\"U1lRBug5Fx\",\"firstName\":\"RA3rNKJO2u\",\"lastName\":\"Ncx0kI1ZaM\",\"displayName\":\"hJF7WiJXQ4\",\"avatarImgPath\":\"Heeg2KIXoK\",\"phone\":\"hqpoIeJGcO\",\"emailId\":\"16oFMrtvze\",\"mobileNo\":\"T8LE2FAxXJ\",\"trustPilotId\":\"l1mTYEUQKT\",\"address\":\"5TEgxGQXpl\",\"aboutMe\":\"mfMqPxgYYZ\",\"bioCoverLetter\":\"UhPAcvf3C3\",\"locateTutorBadge\":\"mAjn2BkzgP\",\"locateTutorSummary\":\"gUMcYMq3c2\",\"genderId\":\"tUgA3x6FcU\",\"currentLocation\":\"IKoV4hIOP5\",\"geoLocation\":\"zOJ2Dg09NE\"}}"}, "url": {"raw": "http://localhost:9766/StudentFavoriteTutor/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "StudentFavoriteTutor", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorAvailabilityOverride", "item": [{"name": "Save TutorAvailabilityOverride", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"uSI4Epe3xB\",\"firstName\":\"pPxTY4SFNy\",\"lastName\":\"kwBXjh6gXr\",\"displayName\":\"hwhMrrZlEc\",\"avatarImgPath\":\"6lC0dFExZK\",\"phone\":\"JnXL4I7kU2\",\"emailId\":\"z3rbccqV5T\",\"mobileNo\":\"pt1fSijwaE\",\"trustPilotId\":\"AexDhc0L8y\",\"address\":\"DBFHWUAz9p\",\"aboutMe\":\"ozg3yM0mDC\",\"bioCoverLetter\":\"GyUP05fu84\",\"locateTutorBadge\":\"Q1SkQacvTr\",\"locateTutorSummary\":\"yrWudsUQTn\",\"genderId\":\"W9C2649KAM\",\"currentLocation\":\"uAHsEl8jee\",\"geoLocation\":\"SClUnQGn2Y\"},\"overrideDate\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "save"]}}}, {"name": "Update TutorAvailabilityOverride", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"v6hkyIdmqs\",\"firstName\":\"l7Lj22iTRh\",\"lastName\":\"JyK9sLPrBM\",\"displayName\":\"Z7LJLgIXRp\",\"avatarImgPath\":\"L6yBamTpUj\",\"phone\":\"ZP4GaP4EOC\",\"emailId\":\"hSmCfTwUWJ\",\"mobileNo\":\"nGvvU8omUt\",\"trustPilotId\":\"8t6TJhtgdK\",\"address\":\"vDBwAzErB2\",\"aboutMe\":\"iNwlgnvX2Z\",\"bioCoverLetter\":\"sWDjz3ne2m\",\"locateTutorBadge\":\"1OV5cpV7fv\",\"locateTutorSummary\":\"nLAV1vbN8l\",\"genderId\":\"DgGa0mxHSE\",\"currentLocation\":\"WNvwEzaeR3\",\"geoLocation\":\"TwygJzqwVH\"},\"overrideDate\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "update", "{value}"]}}}, {"name": "ReadById TutorAvailabilityOverride", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "{value}"]}}}, {"name": "GetAll TutorAvailabilityOverride", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "list"]}}}, {"name": "Dynamic Query TutorAvailabilityOverride", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter TutorAvailabilityOverride", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorAvailabilityOverride", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorAvailabilityOverride", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"KW2vwSM0za\",\"firstName\":\"4HwZMInnoX\",\"lastName\":\"mxm9OwjTjH\",\"displayName\":\"XBnV79aWkG\",\"avatarImgPath\":\"lECHMXsAls\",\"phone\":\"Wt2KjTjUwL\",\"emailId\":\"eNHPdKUJMN\",\"mobileNo\":\"zjBN3S4xl4\",\"trustPilotId\":\"z0Ye6RfVgc\",\"address\":\"7UQNg7yfOB\",\"aboutMe\":\"XPT7DX7vRh\",\"bioCoverLetter\":\"eoc7TyTgCJ\",\"locateTutorBadge\":\"BsRBHeRuTb\",\"locateTutorSummary\":\"exREZmWdmN\",\"genderId\":\"TiwAvUigdb\",\"currentLocation\":\"gRIMgNCQN0\",\"geoLocation\":\"aVqmZxBCFr\"},\"overrideDate\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilityOverride/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilityOverride", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorAvailabilitySlot", "item": [{"name": "Save TutorAvailabilitySlot", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"mZUeMREhNk\",\"firstName\":\"bYggTEbCHo\",\"lastName\":\"Jj81GkVL42\",\"displayName\":\"SEY8oUQCzJ\",\"avatarImgPath\":\"FkWwWruJwQ\",\"phone\":\"wMie3mdhZf\",\"emailId\":\"ulEYyCVMS2\",\"mobileNo\":\"gQA4j89ZHk\",\"trustPilotId\":\"kbpkgban0j\",\"address\":\"Y2uJwcoKDq\",\"aboutMe\":\"IHaF9JGaXV\",\"bioCoverLetter\":\"bjJXVVxXBE\",\"locateTutorBadge\":\"4dL4YlQfGF\",\"locateTutorSummary\":\"s77X41JEuS\",\"genderId\":\"ApVEWl8gED\",\"currentLocation\":\"adY7yl0fpx\",\"geoLocation\":\"aVPLrJxsWU\"},\"dayOfWeek\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "save"]}}}, {"name": "Update TutorAvailabilitySlot", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"XaXExA2uew\",\"firstName\":\"YdUYr0Deld\",\"lastName\":\"VHFGCMSUjz\",\"displayName\":\"QhaA5CdiDq\",\"avatarImgPath\":\"WVe0SMC4pf\",\"phone\":\"ZmZxabafa8\",\"emailId\":\"hEqEGENjhs\",\"mobileNo\":\"ftbxIXH1n0\",\"trustPilotId\":\"Rq7sO1Lkgu\",\"address\":\"KPDFyhfD0U\",\"aboutMe\":\"m55hauHJJr\",\"bioCoverLetter\":\"afgX5KA0MB\",\"locateTutorBadge\":\"lSXb317gbR\",\"locateTutorSummary\":\"vVOycZYzUs\",\"genderId\":\"jxNm2VSqTQ\",\"currentLocation\":\"Wo31sio6PA\",\"geoLocation\":\"k6zST3iPKf\"},\"dayOfWeek\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "update", "{value}"]}}}, {"name": "ReadById TutorAvailabilitySlot", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "{value}"]}}}, {"name": "GetAll TutorAvailabilitySlot", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "list"]}}}, {"name": "Dynamic Query TutorAvailabilitySlot", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter TutorAvailabilitySlot", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorAvailabilitySlot", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorAvailabilitySlot", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"DeEuI4rlWW\",\"firstName\":\"pu0KIeRDJB\",\"lastName\":\"sTdo6wld7G\",\"displayName\":\"WGchg0MZT1\",\"avatarImgPath\":\"jrx3XgbUcR\",\"phone\":\"Qq0eyXOga0\",\"emailId\":\"PfU3OOKrB1\",\"mobileNo\":\"YJ6FMASmdr\",\"trustPilotId\":\"OJaW0ruiBW\",\"address\":\"2w5VQxWBfG\",\"aboutMe\":\"BnKLwxkPtX\",\"bioCoverLetter\":\"Z8UUfjyk72\",\"locateTutorBadge\":\"YRJ47azHXV\",\"locateTutorSummary\":\"PUiPnJikB6\",\"genderId\":\"HWxOAUwDLZ\",\"currentLocation\":\"Rl4bdec4Jr\",\"geoLocation\":\"0gqFKccn26\"},\"dayOfWeek\":0}"}, "url": {"raw": "http://localhost:9766/TutorAvailabilitySlot/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorAvailabilitySlot", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorQualificationAndDocuments", "item": [{"name": "Save TutorQualificationAndDocuments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"f8yX2KiCBE\",\"firstName\":\"KV2nMAru1h\",\"lastName\":\"xckFxebXay\",\"displayName\":\"nJaeiP9aBm\",\"avatarImgPath\":\"G4Eiy2FmKg\",\"phone\":\"jHcQYsub5J\",\"emailId\":\"HjujMGNd4L\",\"mobileNo\":\"orSJ7yMjBg\",\"trustPilotId\":\"vQYPw0airm\",\"address\":\"wkvbohK7vI\",\"aboutMe\":\"tHc8vSaIM7\",\"bioCoverLetter\":\"np1NjtQRGE\",\"locateTutorBadge\":\"DBMdY8m9Fb\",\"locateTutorSummary\":\"8bK1jitlGy\",\"genderId\":\"25IZbl4IZX\",\"currentLocation\":\"hyggGc1J7n\",\"geoLocation\":\"FwdgsRdwx1\"},\"qualificationType\":\"ftbApAgl2s\",\"qualificationField\":\"kcEFZn32pD\",\"universityName\":\"iv4og0UXlz\",\"courseName\":\"Nvt01LA7hd\",\"gradeAchieved\":\"HIHeTXjldI\",\"certificateUrl\":\"AODNTRy4Hm\"}"}, "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "save"]}}}, {"name": "Update TutorQualificationAndDocuments", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"xsG3E8Sykk\",\"firstName\":\"ye9cvVyN6T\",\"lastName\":\"qpSZtOGnZM\",\"displayName\":\"hCdqgaPHfH\",\"avatarImgPath\":\"TZMCcf7Cd8\",\"phone\":\"VJ7xJ4by1w\",\"emailId\":\"3BAwap4Cyn\",\"mobileNo\":\"cHaxmEj7xo\",\"trustPilotId\":\"XpAIRo0Ee0\",\"address\":\"qMVXZJfLa0\",\"aboutMe\":\"XsqIe0j0uS\",\"bioCoverLetter\":\"wuZMbK0YHA\",\"locateTutorBadge\":\"efn1dk0XqS\",\"locateTutorSummary\":\"bYzHRZvd1v\",\"genderId\":\"qIrXZ2BFgv\",\"currentLocation\":\"bucmuZlcsh\",\"geoLocation\":\"Q4Ic5Xx3Xb\"},\"qualificationType\":\"sKp3nGBJR9\",\"qualificationField\":\"vL3k5pYxPR\",\"universityName\":\"dEqVETEiYb\",\"courseName\":\"NC3pmsXM5M\",\"gradeAchieved\":\"JrtD8orbwh\",\"certificateUrl\":\"4cVtdDr2Mn\"}"}, "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "update", "{value}"]}}}, {"name": "ReadById TutorQualificationAndDocuments", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "{value}"]}}}, {"name": "GetAll TutorQualificationAndDocuments", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "list"]}}}, {"name": "Dynamic Query TutorQualificationAndDocuments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter TutorQualificationAndDocuments", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorQualificationAndDocuments", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorQualificationAndDocuments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"KNpefmDiKy\",\"firstName\":\"kpXBs4dY6e\",\"lastName\":\"nmbOYtnlhr\",\"displayName\":\"SYVm71pbww\",\"avatarImgPath\":\"QXNUCO5WMO\",\"phone\":\"ZC6ibHcKcl\",\"emailId\":\"kDccYrghVe\",\"mobileNo\":\"eIe6z05uJz\",\"trustPilotId\":\"A4l7oE7Ypl\",\"address\":\"33eyqvhegF\",\"aboutMe\":\"zDDdqD1fg0\",\"bioCoverLetter\":\"wfbHiVbXbE\",\"locateTutorBadge\":\"D1ENvH6HVQ\",\"locateTutorSummary\":\"U0WqEVJAYb\",\"genderId\":\"c9gHwuWsgk\",\"currentLocation\":\"HUdhbkH7Pa\",\"geoLocation\":\"KEjxiuSQhJ\"},\"qualificationType\":\"QX3v5DQInv\",\"qualificationField\":\"16dPQ5czua\",\"universityName\":\"0qbH2mizwV\",\"courseName\":\"28uc1G4Ncx\",\"gradeAchieved\":\"ur4qqZzXla\",\"certificateUrl\":\"mDR3RDPQpz\"}"}, "url": {"raw": "http://localhost:9766/TutorQualificationAndDocuments/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorQualificationAndDocuments", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorRecommendation", "item": [{"name": "Save TutorRecommendation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"0JNVv1eDub\",\"firstName\":\"HnDBoqQ0Yq\",\"lastName\":\"AVeV0U8ZZ8\",\"displayName\":\"fa3YakNy5U\",\"avatarImgPath\":\"j1P3zoBZo2\",\"phone\":\"tsmlRBZca2\",\"emailId\":\"NpS8hObrsn\",\"mobileNo\":\"s4H32oKYVn\",\"trustPilotId\":\"iEcZmHYHAG\",\"address\":\"SsqOfK30WJ\",\"aboutMe\":\"J18JJSmW5i\",\"bioCoverLetter\":\"ZCjjnpEzo0\",\"locateTutorBadge\":\"hMDWN1L97Z\",\"locateTutorSummary\":\"VBMuY897gh\",\"genderId\":\"9fW5hPkpfd\",\"currentLocation\":\"Gzgr8m0Hz9\",\"geoLocation\":\"WMXqf93XFV\"},\"student\":{\"id\":0,\"salutation\":\"bKO4fy1Xy0\",\"firstName\":\"kBM7RRByWO\",\"lastName\":\"FXndJHefH3\",\"displayName\":\"C0KRRUP745\",\"avatarImgPath\":\"P1H9C0CCvx\",\"phone\":\"hn94a4utda\",\"emailId\":\"bU0gwH71rr\",\"mobileNo\":\"6oqmP5Ug3p\",\"trustPilotId\":\"7zAeXFqhtT\",\"address\":\"0AUKfQBSrw\",\"aboutMe\":\"sveBSWHtD1\",\"bioCoverLetter\":\"6hn8tzAbtE\",\"locateTutorBadge\":\"atLOqhKG5f\",\"locateTutorSummary\":\"en5Nf2VLfv\",\"genderId\":\"4cZDIwFfex\",\"currentLocation\":\"4kCDqRuB7t\",\"geoLocation\":\"r1BEPBAMGt\"},\"recommendationDesc\":\"wHlcbBgDwY\",\"emotionalRating\":\"pBGp57WWD7\"}"}, "url": {"raw": "http://localhost:9766/TutorRecommendation/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "save"]}}}, {"name": "Update TutorRecommendation", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"CB1hSxD9l4\",\"firstName\":\"xemInZeRLs\",\"lastName\":\"VWsX8GiOGP\",\"displayName\":\"O2ol53BrUf\",\"avatarImgPath\":\"Wn7MP2ZpCR\",\"phone\":\"Xa4rwYsJ7v\",\"emailId\":\"bKk1qd9w37\",\"mobileNo\":\"FpJ0e8SKyz\",\"trustPilotId\":\"M9DeYNxdRn\",\"address\":\"4l63UL02jU\",\"aboutMe\":\"CZhN9PLh6P\",\"bioCoverLetter\":\"7nSbTcwabp\",\"locateTutorBadge\":\"OC7eMv2ykz\",\"locateTutorSummary\":\"hzbIva38LE\",\"genderId\":\"nfASGuOPcO\",\"currentLocation\":\"HUu09FcOeL\",\"geoLocation\":\"0eoGBnuK5f\"},\"student\":{\"id\":0,\"salutation\":\"MY8lbMBDDi\",\"firstName\":\"pbHKyJw37n\",\"lastName\":\"Mo4UuBa2on\",\"displayName\":\"Gq5Pd6nY0g\",\"avatarImgPath\":\"7uMC92dhy1\",\"phone\":\"1TnQzAxWUM\",\"emailId\":\"zxSpK0I7uS\",\"mobileNo\":\"pmpITcLRTP\",\"trustPilotId\":\"1p6WP5SvgX\",\"address\":\"Ek39GnhRte\",\"aboutMe\":\"efzmUSIscl\",\"bioCoverLetter\":\"j5FViI5kIq\",\"locateTutorBadge\":\"MgOtAzP2JU\",\"locateTutorSummary\":\"E3IpjmwuHb\",\"genderId\":\"raSBTE1T26\",\"currentLocation\":\"geDXymjbxl\",\"geoLocation\":\"3pa8nsNLkg\"},\"recommendationDesc\":\"q2f5cnhj2b\",\"emotionalRating\":\"cDeS6zCSPr\"}"}, "url": {"raw": "http://localhost:9766/TutorRecommendation/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "update", "{value}"]}}}, {"name": "ReadById TutorRecommendation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorRecommendation/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "{value}"]}}}, {"name": "GetAll TutorRecommendation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorRecommendation/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "list"]}}}, {"name": "Dynamic Query TutorRecommendation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "<PERSON><PERSON>ommendation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorRecommendation/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "filter", "{key}", "{value}"]}}}, {"name": "So<PERSON>Recommendation", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorRecommendation/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorRecommendation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"ZVqSpeIqoB\",\"firstName\":\"vSlQeJDU8U\",\"lastName\":\"oblE0DNDdd\",\"displayName\":\"rECvifGTAF\",\"avatarImgPath\":\"nViZGb2CN3\",\"phone\":\"n5tZO7GEJz\",\"emailId\":\"rJiAOQCdKk\",\"mobileNo\":\"u0McntxIDR\",\"trustPilotId\":\"ihaUD9WJnj\",\"address\":\"opB59ErNVR\",\"aboutMe\":\"Hm5khS20L2\",\"bioCoverLetter\":\"lLKsZHNYpH\",\"locateTutorBadge\":\"TvvomZsYjI\",\"locateTutorSummary\":\"IFsRK5nqEG\",\"genderId\":\"5JvqamqP0j\",\"currentLocation\":\"oDNEI6SlQ0\",\"geoLocation\":\"VIoGOWAqcv\"},\"student\":{\"id\":0,\"salutation\":\"svawEEne3d\",\"firstName\":\"PxMCyJkBCn\",\"lastName\":\"3bc7K6FOw3\",\"displayName\":\"cJuP7BLz5F\",\"avatarImgPath\":\"oprA5jyvA4\",\"phone\":\"1f9am3cYKw\",\"emailId\":\"3jzIL3ZXBS\",\"mobileNo\":\"COjeCF9QaI\",\"trustPilotId\":\"MkWMQ3Ix7v\",\"address\":\"9LUna03sqY\",\"aboutMe\":\"2YejHFq4IE\",\"bioCoverLetter\":\"5rFOSZX2K9\",\"locateTutorBadge\":\"g1CZOmWhoU\",\"locateTutorSummary\":\"fGPCT74TbY\",\"genderId\":\"iccU7t3N6c\",\"currentLocation\":\"juhzbvZvrj\",\"geoLocation\":\"4f9SeXeIRm\"},\"recommendationDesc\":\"oazrwzPruQ\",\"emotionalRating\":\"RKWUsIf90U\"}"}, "url": {"raw": "http://localhost:9766/TutorRecommendation/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorRecommendation", "updatePartialEntity", "{id}"]}}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON> <PERSON><PERSON><PERSON>ie<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"rating\":0,\"comment\":\"dMvEIOiDUl\",\"student\":{\"id\":0,\"salutation\":\"vPsv2GMYGa\",\"firstName\":\"FQ1RfSe8wG\",\"lastName\":\"mLKX587NYN\",\"displayName\":\"hQZAw6JeBm\",\"avatarImgPath\":\"jnIijTzHap\",\"phone\":\"rQRujU7tBh\",\"emailId\":\"GV1K7Y2ZnZ\",\"mobileNo\":\"gsOJyh4ZFn\",\"trustPilotId\":\"unBNKVpMj0\",\"address\":\"aU8DRbjrLv\",\"aboutMe\":\"fQRFEcVrMn\",\"bioCoverLetter\":\"SH1yDEEJZQ\",\"locateTutorBadge\":\"6gv3ADqykK\",\"locateTutorSummary\":\"2risxvouY2\",\"genderId\":\"OadIvUF4wq\",\"currentLocation\":\"cfFFxYvYiT\",\"geoLocation\":\"BVQTOpTsPh\"},\"tutorResponse\":\"CA0s9PdFjQ\"}"}, "url": {"raw": "http://localhost:9766/TutorReview/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save"]}}}, {"name": "Update <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"rating\":0,\"comment\":\"cNJnlej1Oe\",\"student\":{\"id\":0,\"salutation\":\"v2Qn5BMLF1\",\"firstName\":\"d1ppPZ0dVR\",\"lastName\":\"PFNOtBvZ2Y\",\"displayName\":\"xoxYqJpnEc\",\"avatarImgPath\":\"FK7FQ4R36Q\",\"phone\":\"qi39APDWz0\",\"emailId\":\"d4eopUioT6\",\"mobileNo\":\"kQf1WOwzHc\",\"trustPilotId\":\"mdeATGlU1g\",\"address\":\"5eFcWvkK5o\",\"aboutMe\":\"jN9UI8wA4m\",\"bioCoverLetter\":\"ObVAOvjg9i\",\"locateTutorBadge\":\"VGMnJti9oh\",\"locateTutorSummary\":\"bNyGc3aR5E\",\"genderId\":\"H4uhLiwkrH\",\"currentLocation\":\"1fi57Amvvb\",\"geoLocation\":\"xnWtJjeJx9\"},\"tutorResponse\":\"y4uopefH0v\"}"}, "url": {"raw": "http://localhost:9766/TutorReview/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "{value}"]}}}, {"name": "ReadById Tu<PERSON>iew", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorReview/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{value}"]}}}, {"name": "GetAll Tutor<PERSON>eview", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorReview/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "list"]}}}, {"name": "Dynamic Query TutorReview", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorReview/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "{key}", "{value}"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorReview/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorReview", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"booking\":{\"id\":0,\"creditPointsUsed\":0},\"rating\":0,\"comment\":\"mCws9Y6Yqk\",\"student\":{\"id\":0,\"salutation\":\"td7AZ70DQu\",\"firstName\":\"CIOWwnUQsl\",\"lastName\":\"RrQcMAem7M\",\"displayName\":\"M0bADHFqoP\",\"avatarImgPath\":\"NSdTjLiUZ5\",\"phone\":\"Wyg1sSjpu9\",\"emailId\":\"zMQg4Shc4a\",\"mobileNo\":\"2RbaMVzjz9\",\"trustPilotId\":\"CV7a7COsvL\",\"address\":\"YvB1hBkEVC\",\"aboutMe\":\"msJVos60GB\",\"bioCoverLetter\":\"BFhmQSr8ky\",\"locateTutorBadge\":\"TAhQqbQ5c9\",\"locateTutorSummary\":\"Uc85uyE5FI\",\"genderId\":\"ibRmDMSf0H\",\"currentLocation\":\"fIljs2ZOV3\",\"geoLocation\":\"pOXtQzjmBN\"},\"tutorResponse\":\"UUZMiQxNuq\"}"}, "url": {"raw": "http://localhost:9766/TutorReview/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorSettlementConfig", "item": [{"name": "Save TutorSettlementConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"2SSLRhSj2z\",\"firstName\":\"HfRPsW2bCt\",\"lastName\":\"lspFNO7oWz\",\"displayName\":\"y1XMpu7p9v\",\"avatarImgPath\":\"KTiRFOoLlV\",\"phone\":\"aYwrIdDY4w\",\"emailId\":\"ET9S73aqBr\",\"mobileNo\":\"yGAK861E4j\",\"trustPilotId\":\"AlkZ8xg3mG\",\"address\":\"PrZVHHAxzK\",\"aboutMe\":\"lYWcvZtB7W\",\"bioCoverLetter\":\"43OiA3HQsP\",\"locateTutorBadge\":\"J3qVZ8StY9\",\"locateTutorSummary\":\"Q4Kfhti4k3\",\"genderId\":\"KPVYvttUxl\",\"currentLocation\":\"i6LjPmIXIm\",\"geoLocation\":\"F84SGGwtCj\"},\"settlementCycle\":{\"id\":0,\"settlementCycleKey\":\"42TUFWZYA6\",\"settlementCycleName\":\"ikZO37iIVm\"},\"payoutMethod\":\"K3t7SzLCG8\",\"payoutDetails\":\"s1eHC1ZmWc\"}"}, "url": {"raw": "http://localhost:9766/TutorSettlementConfig/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "save"]}}}, {"name": "Update TutorSettlementConfig", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"fS6VDxEItN\",\"firstName\":\"2MWKLADwqB\",\"lastName\":\"T65sDP3Qvs\",\"displayName\":\"wr3JAuhHaz\",\"avatarImgPath\":\"1BdKKkol7M\",\"phone\":\"Aq44GiPYYe\",\"emailId\":\"4D87rYb4hL\",\"mobileNo\":\"drDD3At5sO\",\"trustPilotId\":\"2vw5BhtmF0\",\"address\":\"WubivhpCuK\",\"aboutMe\":\"cGjqnfa2KO\",\"bioCoverLetter\":\"sScC2etOcz\",\"locateTutorBadge\":\"1VDOsZgeO2\",\"locateTutorSummary\":\"djGPuDWdn8\",\"genderId\":\"1Xv9RdhAnd\",\"currentLocation\":\"60cszWlLlB\",\"geoLocation\":\"9O0eVt6zkb\"},\"settlementCycle\":{\"id\":0,\"settlementCycleKey\":\"BBQaK5xuzk\",\"settlementCycleName\":\"3osmV9rQaO\"},\"payoutMethod\":\"a9zhbltKde\",\"payoutDetails\":\"8xSfJYFzCJ\"}"}, "url": {"raw": "http://localhost:9766/TutorSettlementConfig/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "update", "{value}"]}}}, {"name": "ReadById TutorSettlementConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSettlementConfig/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "{value}"]}}}, {"name": "GetAll TutorSettlementConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSettlementConfig/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "list"]}}}, {"name": "Dynamic Query TutorSettlementConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "<PERSON><PERSON>ettlementConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSettlementConfig/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorSettlementConfig", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSettlementConfig/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorSettlementConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutor\":{\"id\":0,\"salutation\":\"SipFRqjDea\",\"firstName\":\"OuP5mbPOc7\",\"lastName\":\"LpaD38eAsi\",\"displayName\":\"8sDArlygVE\",\"avatarImgPath\":\"K26yyaWYeL\",\"phone\":\"xRfzAeoL2s\",\"emailId\":\"p4ud2QGa26\",\"mobileNo\":\"gyintkbJyv\",\"trustPilotId\":\"rnP3mOs94N\",\"address\":\"3Iefabrzpo\",\"aboutMe\":\"3Gb80jlvk1\",\"bioCoverLetter\":\"E1r8S2PgD9\",\"locateTutorBadge\":\"KPCU0vVu5O\",\"locateTutorSummary\":\"PgLgZUXScl\",\"genderId\":\"XCy2CBzVOo\",\"currentLocation\":\"vnx4F97QG3\",\"geoLocation\":\"WdB13QFsCN\"},\"settlementCycle\":{\"id\":0,\"settlementCycleKey\":\"dcSUdj5wV0\",\"settlementCycleName\":\"LH1I03ygRt\"},\"payoutMethod\":\"NZvCtw1QxT\",\"payoutDetails\":\"X2RYnkOF5c\"}"}, "url": {"raw": "http://localhost:9766/TutorSettlementConfig/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSettlementConfig", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorSubject", "item": [{"name": "Save TutorSubject", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"qC1EqA6POd\",\"firstName\":\"SfK7HHpuw5\",\"lastName\":\"TyVLtrkoev\",\"displayName\":\"fOGQ5aTA0A\",\"avatarImgPath\":\"AdVezZBwfJ\",\"phone\":\"VBJ5KL4Bfq\",\"emailId\":\"6ajnkeecWS\",\"mobileNo\":\"OgZIbVjZ6y\",\"trustPilotId\":\"O2G0EUhFD4\",\"address\":\"M9uc5QpVxP\",\"aboutMe\":\"2m2ECawF3N\",\"bioCoverLetter\":\"KcgTBteX0A\",\"locateTutorBadge\":\"xdIsmNmab5\",\"locateTutorSummary\":\"6AUFwxoMEF\",\"genderId\":\"8sFA1h3MqU\",\"currentLocation\":\"oqqVUsqnoE\",\"geoLocation\":\"c6WTq23YmV\"},\"subject\":{\"id\":0,\"subjectKey\":\"coL1T1lXza\",\"subjectShortName\":\"HcOhCKi9Ww\",\"subjectDescription\":\"Ks7KsBDmFh\"},\"videoIntroUrl\":\"tsHxyl3hN6\",\"searchTags\":\"8Vvun82RFh\",\"aboutSubject\":\"SNt4iiQXvw\",\"feesRate\":0,\"billingRateType\":{\"id\":0,\"billingRateTypeKey\":\"Xeq5aKHR97\",\"billingRateType\":\"WXVMgs1btH\"},\"finalFees\":0,\"currencyCode\":\"kSBjVTR4m1\"}"}, "url": {"raw": "http://localhost:9766/TutorSubject/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "save"]}}}, {"name": "Update TutorSubject", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"B10SIWNkAj\",\"firstName\":\"CnpB6DgtS7\",\"lastName\":\"fIHwLSyPPU\",\"displayName\":\"zwgRAzGTDa\",\"avatarImgPath\":\"EjoPj3PUsH\",\"phone\":\"oSb4c2pDnU\",\"emailId\":\"8mO4Xsl9au\",\"mobileNo\":\"4PYcO3AoWj\",\"trustPilotId\":\"HDqLjdycIR\",\"address\":\"DB5prQWfYY\",\"aboutMe\":\"fIERW33KyQ\",\"bioCoverLetter\":\"IrhpOlyupB\",\"locateTutorBadge\":\"TTNkgWNEIB\",\"locateTutorSummary\":\"afRMuOnIva\",\"genderId\":\"toG7FEzNFG\",\"currentLocation\":\"7xoIjFY4EB\",\"geoLocation\":\"npkqoo5kBP\"},\"subject\":{\"id\":0,\"subjectKey\":\"VVce1a8OHh\",\"subjectShortName\":\"PLv4rb6gX2\",\"subjectDescription\":\"8vTGqaPow6\"},\"videoIntroUrl\":\"GG8CzGI0EC\",\"searchTags\":\"SPWEhEiasq\",\"aboutSubject\":\"9WnJRtkTiW\",\"feesRate\":0,\"billingRateType\":{\"id\":0,\"billingRateTypeKey\":\"aMTBarp9Mi\",\"billingRateType\":\"82LBVRnFkp\"},\"finalFees\":0,\"currencyCode\":\"ERDFoBMlks\"}"}, "url": {"raw": "http://localhost:9766/TutorSubject/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "update", "{value}"]}}}, {"name": "ReadById TutorSubject", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSubject/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "{value}"]}}}, {"name": "GetAll TutorSubject", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSubject/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "list"]}}}, {"name": "Dynamic Query TutorSubject", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter TutorSubject", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSubject/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorSubject", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorSubject/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorSubject", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"tutorId\":{\"id\":0,\"salutation\":\"8VeG4mCCTS\",\"firstName\":\"QmWxj7bzZP\",\"lastName\":\"OPVEOGKdr5\",\"displayName\":\"BvwkMJRaWg\",\"avatarImgPath\":\"jT7WP0tRW8\",\"phone\":\"GPLjRhH6gl\",\"emailId\":\"xSRjAG2Fih\",\"mobileNo\":\"FOSQ08ucvM\",\"trustPilotId\":\"vsa2t84Hlu\",\"address\":\"Zdt4cA7lHu\",\"aboutMe\":\"kkfVzkICxA\",\"bioCoverLetter\":\"3sOzabaPHn\",\"locateTutorBadge\":\"OW7MqeHM8b\",\"locateTutorSummary\":\"2Pg0yveJkU\",\"genderId\":\"zcHFj73w9W\",\"currentLocation\":\"NN64IRSjFB\",\"geoLocation\":\"pEI3N9uLNm\"},\"subject\":{\"id\":0,\"subjectKey\":\"IDHVC66LnK\",\"subjectShortName\":\"EZ8qCDPWBC\",\"subjectDescription\":\"KFMdCZ6JkU\"},\"videoIntroUrl\":\"XfULZmSZlA\",\"searchTags\":\"e4rLeJ7NMP\",\"aboutSubject\":\"UQQ3sZLNeL\",\"feesRate\":0,\"billingRateType\":{\"id\":0,\"billingRateTypeKey\":\"Ck58aO8KVh\",\"billingRateType\":\"qetQ9ESkqq\"},\"finalFees\":0,\"currencyCode\":\"D32yzy8BJN\"}"}, "url": {"raw": "http://localhost:9766/TutorSubject/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorSubject", "updatePartialEntity", "{id}"]}}}]}, {"name": "TutorVerificationCall", "item": [{"name": "Save TutorVerificationCall", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"pQTjOcsGw3\",\"firstName\":\"2EKVgohKIt\",\"lastName\":\"Tv1dChmEnQ\",\"displayName\":\"07sZXCVy5b\",\"avatarImgPath\":\"HIWuP73faG\",\"phone\":\"9NwKAlSOmx\",\"emailId\":\"pedQykyZ3J\",\"mobileNo\":\"xlmhV0vWwj\",\"trustPilotId\":\"Vgsd777Vcx\",\"address\":\"xLjlSlpYlH\",\"aboutMe\":\"H5FprNmZ7K\",\"bioCoverLetter\":\"POnT1uINA6\",\"locateTutorBadge\":\"sp5mU6B2HZ\",\"locateTutorSummary\":\"HoDcLldUVm\",\"genderId\":\"4LahCOAvOt\",\"currentLocation\":\"4bI0XsSzsB\",\"geoLocation\":\"lOTIxp9sRF\"},\"meetingUrl\":\"QsALUayImp\",\"status\":{\"id\":0,\"statusName\":\"zEoQsxup7Y\"},\"adminNotes\":\"zYon2CruKz\",\"verificationCallRecordingUrl\":\"OQ5xh1ozzI\"}"}, "url": {"raw": "http://localhost:9766/TutorVerificationCall/save", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "save"]}}}, {"name": "Update TutorVerificationCall", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"4TpbYsCyRz\",\"firstName\":\"NbZwgmv6Yd\",\"lastName\":\"LpH7wwIKKL\",\"displayName\":\"egmDYYkntg\",\"avatarImgPath\":\"wsDs2ci7r4\",\"phone\":\"oVrWJQZWO6\",\"emailId\":\"a0nrQb1EkF\",\"mobileNo\":\"vLEr4jgu7o\",\"trustPilotId\":\"CENfBtQ6Jm\",\"address\":\"fqPKlGrj5w\",\"aboutMe\":\"WJWAQj4klu\",\"bioCoverLetter\":\"2tGiJ6yHRl\",\"locateTutorBadge\":\"EJMAXLEzV2\",\"locateTutorSummary\":\"J1AlKZ2V3h\",\"genderId\":\"NHMDXefLcw\",\"currentLocation\":\"AAa5cqTVXq\",\"geoLocation\":\"EXuDE7jh0J\"},\"meetingUrl\":\"0T7A2qF1Mz\",\"status\":{\"id\":0,\"statusName\":\"hj5d0BAxM7\"},\"adminNotes\":\"EokXblzZcv\",\"verificationCallRecordingUrl\":\"cVLWFegh5z\"}"}, "url": {"raw": "http://localhost:9766/TutorVerificationCall/update/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "update", "{value}"]}}}, {"name": "ReadById TutorVerificationCall", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorVerificationCall/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "{value}"]}}}, {"name": "GetAll TutorVerificationCall", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorVerificationCall/list", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "list"]}}}, {"name": "Dynamic Query TutorVerificationCall", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"query\":\"{query}\",\"parameters\":\"{parameters}\"}"}, "url": {"raw": "http://localhost:9766/getEntitiesByDynamicQuery", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "getEntitiesByDynamicQuery"]}}}, {"name": "Filter TutorVerificationCall", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorVerificationCall/filter/{key}/{value}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "filter", "{key}", "{value}"]}}}, {"name": "Sort TutorVerificationCall", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:9766/TutorVerificationCall/sort/{sortBy}/{sortType}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "sort", "{sortBy}", "{sortType}"]}}}, {"name": "Update Partial TutorVerificationCall", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\":0,\"contact\":{\"id\":0,\"salutation\":\"HruX6zXh97\",\"firstName\":\"Xz2OkvYHog\",\"lastName\":\"lKLFsEKNCD\",\"displayName\":\"8DdbM1jwam\",\"avatarImgPath\":\"5mYAMAdIQ6\",\"phone\":\"O96qkLHIda\",\"emailId\":\"Wff1kZXhqY\",\"mobileNo\":\"xvn51EMUbC\",\"trustPilotId\":\"1WKZFHwcQG\",\"address\":\"Nek1s9kJL3\",\"aboutMe\":\"Trpk9s2TtV\",\"bioCoverLetter\":\"u0f4fp8HfN\",\"locateTutorBadge\":\"qUeFzelCrJ\",\"locateTutorSummary\":\"lTi3Y6165k\",\"genderId\":\"EaJh797MpJ\",\"currentLocation\":\"Q0MCQ89oVD\",\"geoLocation\":\"hEEZpd50Cy\"},\"meetingUrl\":\"DvRA5gMdu1\",\"status\":{\"id\":0,\"statusName\":\"0U9Z7HYDdC\"},\"adminNotes\":\"22pYO9UnpV\",\"verificationCallRecordingUrl\":\"11HtzJIxqJ\"}"}, "url": {"raw": "http://localhost:9766/TutorVerificationCall/updatePartialEntity/{id}", "protocol": "http", "host": ["localhost"], "port": "9766", "path": ["api", "TutorVerificationCall", "updatePartialEntity", "{id}"]}}}]}]}