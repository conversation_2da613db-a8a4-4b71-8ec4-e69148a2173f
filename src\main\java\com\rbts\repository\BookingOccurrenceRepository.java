package com.rbts.repository;

import com.rbts.entity.Booking;
import com.rbts.entity.BookingOccurrence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingOccurrenceRepository extends JpaRepository<BookingOccurrence, Long> {

    List<BookingOccurrence> findByBooking(Booking booking);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.occurrenceTime BETWEEN :startTime AND :endTime")
    List<BookingOccurrence> findByOccurrenceTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking = :booking AND bo.occurrenceTime = :occurrenceTime")
    Optional<BookingOccurrence> findByBookingAndOccurrenceTime(@Param("booking") Booking booking, @Param("occurrenceTime") LocalDateTime occurrenceTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.session.tutorId = :tutor AND bo.occurrenceTime BETWEEN :startTime AND :endTime")
    List<BookingOccurrence> findByTutorAndTimeRange(@Param("tutor") com.rbts.entity.ContactDetails tutor, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.students = :student AND bo.occurrenceTime BETWEEN :startTime AND :endTime")
    List<BookingOccurrence> findByStudentAndTimeRange(@Param("student") com.rbts.entity.ContactDetails student, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT COUNT(bo) FROM BookingOccurrence bo WHERE bo.booking = :booking")
    Long countByBooking(@Param("booking") Booking booking);
}
