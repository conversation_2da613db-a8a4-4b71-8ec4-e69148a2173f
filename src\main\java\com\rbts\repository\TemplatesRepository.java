package com.rbts.repository;

import com.rbts.entity.auth.Templates;
import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Category entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TemplatesRepository extends JpaRepository<Templates, Long> {


    @Query(value = "SELECT id, template_subject, template_header, template_body, template_footer FROM public.templates WHERE id = :id", nativeQuery = true)
    Tuple findTemplateTupleById(@Param("id") Long id);

}

