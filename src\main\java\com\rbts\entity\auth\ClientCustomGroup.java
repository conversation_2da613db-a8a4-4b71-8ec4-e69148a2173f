package com.rbts.entity.auth;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "client_custom_groups")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClientCustomGroup extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "clientcustomgroupsequencegenerator")
    @GenericGenerator(
            name = "clientcustomgroupsequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "clientcustomgroupsequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @NotNull
    @Column(name = "group_key", nullable = false, unique = true)
    private String groupKey;

    @NotNull
    @Column(name = "group_name", nullable = false)
    private String groupName;

    @NotNull
    @Column(name = "group_desc", nullable = false)
    private String groupDesc;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @NotNull
    @Column(name = "is_super_admin", nullable = false)
    private Boolean isSuperAdmin;

}

