package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "query_master")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMaster extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "querymastersequencegenerator")
    @GenericGenerator(
            name = "querymastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "querymastersequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;


    @NotNull
    @Column(name = "query", nullable = false,unique = true, columnDefinition = "TEXT")
    private String query;

    @NotNull
    @Column(name = "query_name", nullable = false,unique = true, columnDefinition = "TEXT")
    private String queryName;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "query_for_id", nullable = false)
    private QueryForMaster queryForId;
}