package com.rbts.entity.auth;

import jakarta.validation.constraints.NotNull;
import lombok.*;

import jakarta.persistence.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_roles_details")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserRolesDetails extends Auditable{

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "useroledetailssequencegenerator")
    @SequenceGenerator(name = "useroledetailssequencegenerator", allocationSize = 1, initialValue = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "user_id")
    private Users user;

    @NotNull
    @Column(name = "role_name",nullable = false)
    private String roleName;

    @NotNull
    @Column(name = "status_name",nullable = false)
    private String statusName;

}
