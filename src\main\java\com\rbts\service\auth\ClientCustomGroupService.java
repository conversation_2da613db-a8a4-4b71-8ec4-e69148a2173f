package com.rbts.service.auth;

import com.rbts.entity.auth.ClientCustomGroup;
import com.rbts.repository.ClientCustomGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ClientCustomGroupService {

    private final ClientCustomGroupRepository repository;

    public ClientCustomGroup create(ClientCustomGroup clientCustomGroup) {
        if (repository.existsByGroupKey(clientCustomGroup.getGroupKey())) {
            throw new RuntimeException("Group key already exists");
        }
        return repository.save(clientCustomGroup);
    }

    public ClientCustomGroup update(Long id, ClientCustomGroup clientCustomGroup) {
        ClientCustomGroup existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("ClientCustomGroup not found with ID: " + id));
        existing.setCompanyId(clientCustomGroup.getCompanyId());
        existing.setGroupKey(clientCustomGroup.getGroupKey());
        existing.setGroupName(clientCustomGroup.getGroupName());
        existing.setGroupDesc(clientCustomGroup.getGroupDesc());
        existing.setIsActive(clientCustomGroup.getIsActive());
        existing.setIsSuperAdmin(clientCustomGroup.getIsSuperAdmin());

        return repository.save(existing);
    }

    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new RuntimeException("ClientCustomGroup not found with ID: " + id);
        }
        repository.deleteById(id);
    }

    public Optional<ClientCustomGroup> getById(Long id) {
        return repository.findById(id);
    }

    public List<ClientCustomGroup> getAll() {
        return repository.findAll();
    }
}
