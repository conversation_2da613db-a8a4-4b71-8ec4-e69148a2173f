package com.rbts.service.auth;

import com.rbts.dto.NotificationDto;
import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.auth.Passwords;
import com.rbts.entity.auth.UserRolesDetails;
import com.rbts.entity.auth.Users;
import com.rbts.exception.NullPointerException;
import com.rbts.repository.PasswordsRepository;
import com.rbts.repository.UsersRepository;
import com.rbts.exception.AppProperties;
import com.rbts.exception.DuplicateDataException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@Service
@Transactional
@Slf4j
public class PasswordsService {


    @Autowired
    PasswordEncoder encoder;

    @Autowired
    PasswordsRepository passwordsRepository;

    @Autowired
    OtpRedisService otpRedisService;

    @Autowired
    EmailService emailService;


    @Autowired
    UsersRepository usersRepository;


    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Autowired
    UserRolesDetailsService userRolesDetailsService ;

    @Autowired
    AppProperties appProperties;




    public ResponseEntity<String> changePassword(String newPassword, String email) throws Exception {
        if (email== null || newPassword == null){
            throw new NullPointerException(appProperties.getNullData());
        }
        Users users = usersRepository.findByUsername(base64EncryptionUtils.encrypt(email));
        List<UserRolesDetails> rolesDetails =userRolesDetailsService.getByUserId(users.getId());
        if (users != null) {
            List<Passwords> recentPasswords = passwordsRepository.findTop5ByUserIdOrderByCreatedDateDesc(users.getId());
            // Check that the new password is not one of the five most recent passwords
            boolean isNewPasswordRecent = recentPasswords.stream()
                    .anyMatch(p -> {
                        try {
                            return encoder.matches(newPassword, p.getPassword());
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
            if (isNewPasswordRecent) {
                try {
                    throw new DuplicateDataException(appProperties.getNewPassword());
                } catch (DuplicateDataException e) {
                    throw new RuntimeException(e);
                }
            }

            usersRepository.save(Users.builder()
                    .id(users.getId())
                    .status(users.getStatus())
                    .password(hashedPassword(newPassword,users))
                    .username(base64EncryptionUtils.decrypt(users.getUsername()))
                    .build());

            boolean isSuperAdmin = rolesDetails.stream()
                    .anyMatch(role -> "SA".equalsIgnoreCase(role.getRoleName()));

            if (isSuperAdmin) {
                emailService.sendNotification(NotificationDto.builder()
                        .to(email)
                        .username(users.getUsername())
                        .build(), 3L);
            }
        }
        return ResponseEntity.ok(appProperties.getUser1());
    }
    public String hashedPassword(String newPassword, Users users ){
        String encode = encoder.encode(newPassword);
        try {
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Passwords passwords = new Passwords();
            passwords.setUser(users);
            passwords.setPassword(encode);
            passwords.setCreatedDate(new Date());
            passwordsRepository.save(passwords);
        return encode;
    }
    public ResponseEntity<String> verifyOtp(String email, int otp) throws Exception {
        log.info("Verifying OTP for email: {}", email);

        Integer oldOtp = Integer.valueOf(otpRedisService.getOtp(email));

        if (oldOtp.equals(otp)) {
            otpRedisService.removeOtp(email); // Optional: clean up
            return ResponseEntity.status(201).body(appProperties.getOtpMsg2());
        } else {
            return ResponseEntity.status(400).body(appProperties.getOtpMsg3());
        }
    }

    public ResponseEntity<String> validateEmail(String email, String name, HttpServletRequest request) throws Exception {
        String tenantId = request.getHeader("X-TenantID");
        log.info("Incoming email: {}, name: {}", email, name);

        if (tenantId == null || tenantId.isEmpty()) {
            throw new IllegalArgumentException("Missing tenantId header");
        }

        int otp = ThreadLocalRandom.current().nextInt(99999, 999999);
        boolean flag = false;

        if ("public".equalsIgnoreCase(tenantId)) {
            flag = emailService.sendNotification(NotificationDto.builder()
                    .to(email)
                    .username(name)
                    .otp(otp)
                    .build(), 2L);
            log.info("OTP sent: {}, status: {}", otp, flag);
        }

        if (flag) {
            otpRedisService.storeOtp(email, String.valueOf(otp), 5); // 5 minute expiry
            log.info("OTP stored in Redis for email: {}", email);
            return ResponseEntity.status(201).body(appProperties.getOtpMsg());
        } else {
            log.warn("Failed to send OTP notification");
            return ResponseEntity.status(500).body(appProperties.getOtpMsg1());
        }
    }

}







