package com.rbts.repository;


import com.rbts.entity.auth.UserRolesDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRoleDetailsRepository extends JpaRepository<UserRolesDetails, Long> {
    List<UserRolesDetails> findByUserId(Long userId);
//    UserRolesDetails findFirstByUserId(Long userId);


    @Query("SELECT urd FROM UserRolesDetails urd WHERE urd.roleName = :roleId")
    List<UserRolesDetails> findByRoleMaster(@Param("roleId") Long roleId);

}
