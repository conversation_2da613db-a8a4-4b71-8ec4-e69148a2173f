package com.rbts.exception;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.DataException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(CustomException.class)
    public ResponseEntity<Object> handleCustomException(CustomException ex) {
        Throwable cause = ex.getCause();
        Map<String, Object> errorDetails = new HashMap<>();

        log.error("CustomException caught: {}", ex.getMessage(), ex);

        if (cause instanceof FeignClientException) {
            log.warn("FeignClientException detected inside CustomException, skipping handling to preserve 702 status");
            try {
                throw (FeignClientException) cause; // Will be handled by @ExceptionHandler(FeignClientException.class)
            } catch (FeignClientException e) {
                throw new RuntimeException(e);
            }
        }

        if (cause instanceof jakarta.validation.ConstraintViolationException validationEx) {
            Set<ConstraintViolation<?>> violations = validationEx.getConstraintViolations();
            StringBuilder errorMessage = new StringBuilder();

            for (ConstraintViolation<?> violation : violations) {
                errorMessage.append(violation.getPropertyPath())
                        .append(" ")
                        .append(violation.getMessage())
                        .append("; ");
            }
            errorDetails.put("error", errorMessage.toString().trim());

            log.warn("ConstraintViolationException handled with 701: {}", errorMessage);
            return ResponseEntity.status(700).body(errorDetails);
        }
        if (cause instanceof ConstraintViolationException) {
            String cleanMessage = extractDetailMessage(cause);
            errorDetails.put("error", cleanMessage);
            log.warn("ConstraintViolationException handled: {}", cleanMessage);
            if (cleanMessage.toLowerCase().contains("is not present in table")) {
                // Foreign key violation
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorDetails);
            }
            return ResponseEntity.status(701).body(errorDetails);
        }

        if (cause instanceof DataException) {
            String errorMsg = cause.getLocalizedMessage();
            errorDetails.put("error", errorMsg);
            log.warn("DataException (value too long etc.) handled: {}", errorMsg);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDetails);
        }

        if (cause instanceof DataIntegrityViolationException) {
            String errorMsg = cause.getMessage();
            errorDetails.put("error", errorMsg);
            log.warn("DataIntegrityViolationException handled: {}", errorMsg);
            return new ResponseEntity<>(errorDetails, HttpStatus.CONFLICT);
        }

        if (cause instanceof EntityNotFoundException) {
            String errorMsg = cause.getMessage();
            errorDetails.put("error", errorMsg);
            log.warn("EntityNotFoundException handled: {}", errorMsg);
            return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);
        }

        String fallbackMessage = cause != null ? cause.getMessage() : ex.getMessage();
        errorDetails.put("error", fallbackMessage);
        log.error("Unhandled exception caught: {}", fallbackMessage, ex);
        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private String extractDetailMessage(Throwable e) {
        while (e != null) {
            String msg = e.getMessage();
            if (msg != null && msg.contains("Detail:")) {
                int start = msg.indexOf("Detail:");
                int end = msg.indexOf("Call getNextException");
                if (end > start) {
                    return msg.substring(start, end).trim();
                }
                return msg.substring(start).trim();
            }
            e = e.getCause();
        }
        return e != null ? e.getLocalizedMessage() : "Unknown error detail";
    }


    @ExceptionHandler(FeignClientException.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public @ResponseBody ResponseEntity<ErrorResponse> handleFeignClientException(final FeignClientException exception) {
        ErrorResponse error = new ErrorResponse();
        error.setError("Feign Client Error");
        error.setMessage(exception.getMessage());
        error.setTimestamp(System.currentTimeMillis());
        log.error("FeignClientException: {}", exception.getMessage());
        return ResponseEntity.status(702).body(error);
    }

}
