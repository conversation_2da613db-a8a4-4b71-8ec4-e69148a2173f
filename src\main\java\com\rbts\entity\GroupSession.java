//package com.rbts.entity;
//
//import jakarta.persistence.*;
//import lombok.*;
//
//@Entity
//@Table(name = "group_sessions")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class GroupSession {
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "groupSessionSeqGen")
//    @SequenceGenerator(name = "groupSessionSeqGen", sequenceName = "group_sessions_seq", allocationSize = 1)
//    @Column(name = "id")
//    private Long id;
//
//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "session_id", nullable = false)
//    private Session session;
//
//    @Column(name = "min_students")
//    private Long minStudents;
//
//    @Column(name = "max_students")
//    private Long maxStudents;
//}
