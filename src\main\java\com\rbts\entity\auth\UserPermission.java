    package com.rbts.entity.auth;

    import jakarta.persistence.*;
    import jakarta.validation.constraints.NotNull;
    import lombok.*;
    import org.hibernate.annotations.GenericGenerator;

    @EqualsAndHashCode(callSuper = true)
    @Entity
    @Table(name = "user_permissions")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public class UserPermission extends Auditable {
        @Id
        @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userpermissionsequencegenerator")
        @GenericGenerator(
                name = "userpermissionsequencegenerator",
                strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
                parameters = {
                        @org.hibernate.annotations.Parameter(name = "sequence_name", value = "userpermissionsequencegenerator"),
                        @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                        @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
                }
        )
        @Column(name = "id")
        private Long id;

        @NotNull
        @ManyToOne
        @JoinColumn(name = "user_id", nullable = false)
        private Users userId;

        @NotNull
        @Column(name = "permission_key", nullable = false)
        private String permissionKey;

        @NotNull
        @Column(name = "is_active", nullable = false)
        private Boolean isActive;
    }