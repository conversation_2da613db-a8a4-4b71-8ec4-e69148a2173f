package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(  name = "session_locations")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionLocation {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sessionLocationSeqGen")
    @SequenceGenerator(name = "sessionLocationSeqGen", sequenceName = "session_location_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "location_key", nullable = false, unique = true)
    private String locationKey;

    @NotNull
    @Column(name = "location_value", nullable = false)
    private String locationValue;

    @Column(name = "is_active")
    private Boolean isActive;
}
