package com.rbts.controller.auth;

import com.rbts.entity.auth.CustomGroupPermission;
import com.rbts.service.auth.CustomGroupPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
@RestController
@RequestMapping("/api/user/custom-group-permissions")
@RequiredArgsConstructor
public class CustomGroupPermissionController {

    private final CustomGroupPermissionService service;

    @PostMapping("/save")
    public ResponseEntity<CustomGroupPermission> create(@RequestBody CustomGroupPermission permission) {
        return ResponseEntity.ok(service.create(permission));
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomGroupPermission> update(@PathVariable Long id, @RequestBody CustomGroupPermission permission) {
        return ResponseEntity.ok(service.update(id, permission));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomGroupPermission> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/getAll")
    public ResponseEntity<List<CustomGroupPermission>> getAll() {
        return ResponseEntity.ok(service.getAll());
    }
}
