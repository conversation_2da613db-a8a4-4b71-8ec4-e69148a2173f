package com.rbts.entity.master;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "billing_rate_type")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillingRateType {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "billingRateSeqGen")
    @SequenceGenerator(name = "billingRateSeqGen", sequenceName = "billing_rate_type_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "billing_rate_type_key", nullable = false, unique = true)
    private String billingRateTypeKey;

    @Column(name = "billing_rate_type", nullable = false)
    private String billingRateType;
}
