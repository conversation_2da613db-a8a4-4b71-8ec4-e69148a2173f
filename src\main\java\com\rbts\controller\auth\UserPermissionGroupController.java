package com.rbts.controller.auth;

import com.rbts.entity.auth.UserPermissionGroup;
import com.rbts.service.auth.UserPermissionGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
@RestController
@RequestMapping("/api/user/user-permission-groups")
@RequiredArgsConstructor
public class UserPermissionGroupController {

    private final UserPermissionGroupService service;

    @PostMapping("/save")
    public ResponseEntity<UserPermissionGroup> create(@RequestBody UserPermissionGroup userPermissionGroup) {
        return ResponseEntity.ok(service.create(userPermissionGroup));
    }

    @PutMapping("/{id}")
    public ResponseEntity<UserPermissionGroup> update(@PathVariable Long id, @RequestBody UserPermissionGroup userPermissionGroup) {
        return ResponseEntity.ok(service.update(id, userPermissionGroup));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }
    @GetMapping("/{id}")
    public ResponseEntity<UserPermissionGroup> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    @GetMapping("/getAll")
    public ResponseEntity<List<UserPermissionGroup>> getAll() {
        return ResponseEntity.ok(service.getAll());
    }

    @GetMapping("/getByUserId/{id}")
    public List<UserPermissionGroup> getByUserId(@PathVariable Long id) {
        return service.getByUserId(id);
    }
}
