//package com.rbts.repository;
//
//import com.rbts.entity.Temp;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.stereotype.Repository;
//
//import java.time.LocalDateTime;
//
//@Repository
//public interface TempRepository extends JpaRepository<Temp,Long> {
//
//    void deleteByCreatedAtBefore(LocalDateTime expiryTime);
//
////    Temp findByUsername(String username);
//
//    Temp findFirstByUsernameOrderByCreatedAtDesc(String username);
//}
