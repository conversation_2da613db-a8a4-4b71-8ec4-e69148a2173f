package com.rbts.entity.auth;



import com.rbts.encryption.Encrypt;
import com.rbts.encryption.EncryptionEntityListener;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name="user_details",uniqueConstraints = {
        @UniqueConstraint(columnNames = "username")})
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EntityListeners(EncryptionEntityListener.class)
public class Users extends Auditable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userSequenceGenerator")
    @SequenceGenerator(name = "userSequenceGenerator",allocationSize = 1, initialValue = 10000)
    @Column(name = "id")
    private Long id;

    @Encrypt
    @NotNull
    @Column(name="username", nullable = false)
    private String username;

    @Column(name="password")
    private String password;

    @Column(name="status_name", nullable = false)
    private String status;
}