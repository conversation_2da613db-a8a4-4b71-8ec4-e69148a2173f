package com.rbts.service.auth;


import com.rbts.dto.NotificationDto;
import com.rbts.entity.auth.Templates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.microsoft.graph.models.*;
import com.microsoft.graph.models.Message;


import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.requests.GraphServiceClient;

import java.io.IOException;

import java.util.*;
import java.util.List;

@Service
@Slf4j
public class EmailService {


    @Autowired
    Velocitytemp velocitytemp;

    @Autowired
    TemplatesService templatesService;

    public boolean sendNotification(NotificationDto notificationDto, Long templateId) throws IOException {
      log.info("Notification"+templateId);
       Templates templates = templatesService.getTemplatesById(templateId);
        log.info("Notification1Data"+templates);
         String message =   velocitytemp.generateTemplate2(notificationDto, templates);
        log.info("Notification2Data"+message);
        String subject = templates.getTemplateSubject();
         String to = notificationDto.getTo();
       return sendEmail(to,null,null,subject,message,null,null);
    }
    public boolean sendEmail(String to, String[] cc, String bcc, String subject, String messageContent, String base64Data, String fileName) throws IOException {
        String clientId = "9898f5b8-aba7-4b24-9a1f-a0d8152fd377";
        String clientSecret = "****************************************";
        String tenantId = "14158288-a340-4380-88ed-a8989a932425";
        // Authenticate with Azure AD
        ClientSecretCredential clientSecretCredential = new ClientSecretCredentialBuilder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .tenantId(tenantId)
                .build();
        TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(
                Collections.singletonList("https://graph.microsoft.com/.default"),
                clientSecretCredential
        );
        // Create Graph client
        GraphServiceClient<?> graphClient = GraphServiceClient.builder()
                .authenticationProvider(authProvider)
                .buildClient();
        // Create email message
        Message message = new Message();
        message.subject = subject;
        // Set email body
        ItemBody body = new ItemBody();
        body.contentType = BodyType.HTML;
        body.content = messageContent;
        message.body = body;

        // Set To recipient
        if (to != null && !to.trim().isEmpty()) {
            List<Recipient> toRecipients = new ArrayList<>();
            Recipient toRecipient = new Recipient();
            EmailAddress toEmail = new EmailAddress();
            toEmail.address = to;
            toRecipient.emailAddress = toEmail;
            toRecipients.add(toRecipient);
            message.toRecipients = toRecipients;
        }

        // Set CC recipients
        if (cc != null && cc.length > 0) {
            List<Recipient> ccRecipients = new ArrayList<>();
            for (String ccEmail : cc) {
                if (ccEmail != null && !ccEmail.trim().isEmpty()) {
                    Recipient ccRecipient = new Recipient();
                    EmailAddress ccAddress = new EmailAddress();
                    ccAddress.address = ccEmail;
                    ccRecipient.emailAddress = ccAddress;
                    ccRecipients.add(ccRecipient);
                }
            }
            if (!ccRecipients.isEmpty()) {
                message.ccRecipients = ccRecipients;
            }
        }

        // Set BCC recipients
        if (bcc != null && !bcc.trim().isEmpty()) {
            List<Recipient> bccRecipients = new ArrayList<>();
            Recipient bccRecipient = new Recipient();
            EmailAddress bccEmail = new EmailAddress();
            bccEmail.address = bcc;
            bccRecipient.emailAddress = bccEmail;
            bccRecipients.add(bccRecipient);
            message.bccRecipients = bccRecipients;
        }


//        if (base64Data != null && !base64Data.isEmpty()) {
//            byte[] fileBytes = Base64.getDecoder().decode(base64Data);
//
//            // Save file to temporary path
//            Path tempFilePath = Files.createTempFile(fileName, ".pdf");
//            Files.write(tempFilePath, fileBytes, StandardOpenOption.WRITE);
//
//            // Read the saved file as bytes
//            byte[] fileContent = Files.readAllBytes(tempFilePath);
//
//            FileAttachment attachment = new FileAttachment();
//            attachment.oDataType = "#microsoft.graph.fileAttachment";
//            attachment.name = fileName + ".pdf";
//            attachment.contentBytes = fileContent;
//            attachment.contentType = "application/pdf";
//
//            List<Attachment> attachmentsList = new ArrayList<>();
//            attachmentsList.add(attachment);
//
//            AttachmentCollectionResponse response = new AttachmentCollectionResponse();
//            response.value = attachmentsList;
//
//            message.attachments = new AttachmentCollectionPage(response, null);
//        }

        try {
            String senderEmail = "<EMAIL>";
            graphClient.users(senderEmail)
                    .sendMail(UserSendMailParameterSet.newBuilder()
                            .withMessage(message)
                            .withSaveToSentItems(true)
                            .build())
                    .buildRequest()
                    .post();

            System.out.println("Email sent successfully with attachment.");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println(" Error sending email: " + e.getMessage());
        }
        return false;
    }
}
