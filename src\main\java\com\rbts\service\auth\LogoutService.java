package com.rbts.service.auth;


import com.rbts.dto.MessageResponse;
import com.rbts.encryption.Base64EncryptionUtils;
import com.rbts.entity.auth.LoginDetails;
import com.rbts.exception.AppProperties;
import com.rbts.repository.LoginDetailsRepository;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.time.LocalDateTime;

@Service
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogoutService {

    @Autowired
    Base64EncryptionUtils base64EncryptionUtils;

    @Value("${app.jwtSecret}")
    private String jwtSecret ;

    @Autowired
    LoginDetailsRepository loginDetailsRepository;

    @Autowired
    AppProperties appProperties;


    public ResponseEntity<MessageResponse> logout(HttpServletRequest request) throws Exception {
        String authorizationHeader = request.getHeader("Authorization");
        String username = null;
        //Boolean users;
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String token = authorizationHeader.substring(7); // Remove "Bearer " prefix

            // Reconstruct the same key used for signing
            Key key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));

            Claims claims = Jwts.parser()
                    .setSigningKey(key)
                    .parseClaimsJws(token)
                    .getBody();

             username = claims.getSubject();
        }

        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            LoginDetails details=loginDetailsRepository.findByUsername3(username);
            if (details == null) {
                return ResponseEntity.ok(new MessageResponse(appProperties.getLoggedOut()));
            }

            details.setLogoutTime(LocalDateTime.now());
            details.setActive(false);
            loginDetailsRepository.save(details);
            return ResponseEntity.ok(new MessageResponse(appProperties.getLoggedOutSuccess()));
        }
        return ResponseEntity.badRequest().body(new MessageResponse(appProperties.getInvalidToken()));
    }
}
