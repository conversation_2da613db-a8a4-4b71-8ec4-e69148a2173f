package com.rbts.service.common;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rbts.dto.QueryRequestDto;
import com.rbts.encryption.EncryptionEntityListener;
import com.rbts.entity.QueryMaster;
import com.rbts.exception.CustomException;
import com.rbts.exception.FeignClientException;
import jakarta.persistence.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.InvalidClassException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DynamicEntityService {

    private final EntityManager entityManager;
    private final ObjectMapper objectMapper;
    EncryptionEntityListener encryptionEntityListener;
    private final HttpClient httpClient = HttpClient.newHttpClient();



    @Transactional
    public Map<String, Object> saveEntity(String entityName, Map<String, Object> entityData) {
        try {
            log.info("Saving entity: {}", entityName);
            Class<?> entityClass = getEntityClass(entityName);
            Object entity = createEntityFromMap(entityClass, entityData);
            entityManager.persist(entity);
            entityManager.flush();
            log.info("Entity saved: {}", entity);
            return convertEntityToMap(entity);
        } catch (Exception e) {
            log.error("Error saving entity: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> listEntities(String entityName) {
        try {
            log.info("Listing all entities of: {}", entityName);
            Class<?> entityClass = getEntityClass(entityName);
            List<?> entities = entityManager.createQuery("FROM " + entityClass.getSimpleName(), entityClass).getResultList();
            return entities.stream().map(this::convertEntityToMap).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error listing entities: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }

    @Transactional
    public Map<String, Object> updateEntity(String entityName, Long id, Map<String, Object> entityData) {
        try {
            log.info("Updating entity: {}, ID: {}", entityName, id);
            Class<?> entityClass = getEntityClass(entityName);
            Object entity = entityManager.find(entityClass, id);
            if (entity == null) {
                throw new EntityNotFoundException("Entity not found with id: " + id);
            }
            updateEntityFromMap(entity, entityData);
            entityManager.merge(entity);
            entityManager.flush();
            return convertEntityToMap(entity);
        } catch (Exception e) {
            log.error("Error updating entity: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }

    @Transactional
    public void deleteEntity(String entityName, Long id) {
        try {
            log.info("Deleting entity: {}, ID: {}", entityName, id);
            Class<?> entityClass = getEntityClass(entityName);
            Object entity = entityManager.find(entityClass, id);
            if (entity == null) {
                throw new EntityNotFoundException("Entity not found with id: " + id);
            }
            entityManager.remove(entity);
            log.info("Entity deleted successfully");
        } catch (Exception e) {
            log.error("Error deleting entity: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }



    private Class<?> getEntityClass(String entityName) {
        String basePackage = "com.rbts.entity";
        try {
            // Scan all sub-packages under basePackage
            Reflections reflections = new Reflections(basePackage);
            Set<Class<?>> allEntities = reflections.getTypesAnnotatedWith(Entity.class);

            for (Class<?> clazz : allEntities) {
                if (clazz.getSimpleName().equalsIgnoreCase(entityName)) {
                    return clazz;
                }
            }

            throw new ClassNotFoundException("Entity class not found for name: " + entityName);

        } catch (Exception e) {
            log.error("Invalid entity class: {}", entityName, e);
            throw new RuntimeException("Invalid entity name: " + entityName, e);
        }
    }


    private Object createEntityFromMap(Class<?> entityClass, Map<String, Object> data) {
        try {
            Object entity = entityClass.getDeclaredConstructor().newInstance();
            updateEntityFromMap(entity, data);
            return entity;
        } catch (Exception e) {
            log.error("Failed to create entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create entity instance for class: " + entityClass.getSimpleName(), e);
        }
    }

    private void updateEntityFromMap(Object entity, Map<String, Object> data) {
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            try {
                Field field = entity.getClass().getDeclaredField(entry.getKey());
                field.setAccessible(true);
                Object value = entry.getValue();

                if (field.getType().equals(Long.class) && value instanceof Integer) {
                    value = ((Integer) value).longValue();
                }

                if (value instanceof Map && !field.getType().isAssignableFrom(Map.class)) {
                    Object nestedEntity = createEntityFromMap(field.getType(), (Map<String, Object>) value);
                    field.set(entity, nestedEntity);
                } else if (field.getType().isAssignableFrom(Set.class) && value instanceof List) {
                    // Convert List to Set
                    Set<?> setValue = new HashSet<>((List<?>) value);
                    field.set(entity, setValue);
                } else {
                    field.set(entity, convertValue(field.getType(), value));
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.warn("Skipping field {} due to error: {}", entry.getKey(), e.getMessage());
            }
        }
    }


    private Map<String, Object> convertEntityToMap(Object entity) {
        Map<String, Object> result = new HashMap<>();
        for (Field field : entity.getClass().getDeclaredFields()) {
            try {
                field.setAccessible(true);
                result.put(field.getName(), field.get(entity));
            } catch (IllegalAccessException e) {
                log.warn("Could not access field: {}", field.getName());
            }
        }
        return result;
    }

    private Object convertValue(Class<?> fieldType, Object value) {
        if (value == null) return null;

        try {
            if (fieldType.equals(Date.class) && value instanceof String) {
                return new SimpleDateFormat("yyyy-MM-dd").parse((String) value);
            }
            if (fieldType.equals(LocalDate.class) && value instanceof String) {
                return LocalDate.parse((String) value);
            }
            if (fieldType.equals(BigDecimal.class)) {
                if (value instanceof Number) {
                    return BigDecimal.valueOf(((Number) value).doubleValue());
                } else if (value instanceof String) {
                    return new BigDecimal((String) value);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to convert value for type {}: {}", fieldType.getSimpleName(), e.getMessage());
        }

        return value;
    }


    @Transactional
    public Map<String, Object> partialUpdateEntity(String entityName, Long id, Map<String, Object> updates) {
        try {
            log.info("Starting partial update for entity: {}, ID: {}", entityName, id);

            Class<?> entityClass = getEntityClass(entityName);
            Object entity = entityManager.find(entityClass, id);

            if (entity == null) {
                String msg = String.format("Entity %s with ID %d not found", entityName, id);
                log.warn(msg);
                throw new EntityNotFoundException(msg);
            }

            for (Map.Entry<String, Object> entry : updates.entrySet()) {
                String fieldName = entry.getKey();
                Object newValue = entry.getValue();

                try {
                    Field field = entityClass.getDeclaredField(fieldName);
                    field.setAccessible(true);

                    Object oldValue = field.get(entity); // optional but useful for debugging

                    Object convertedValue = convertValue(field.getType(), newValue);
                    field.set(entity, convertedValue);

                    log.info("Updated field '{}' from '{}' to '{}'", fieldName, oldValue, convertedValue);
                } catch (NoSuchFieldException e) {
                    log.warn("Field '{}' not found in entity '{}'. Skipping this field.", fieldName, entityName);
                } catch (IllegalAccessException e) {
                    log.error("Illegal access while setting field '{}' in entity '{}': {}", fieldName, entityName, e.getMessage(), e);
                    throw new CustomException("Illegal access to field '" + fieldName + "'", e);
                } catch (Exception e) {
                    log.error("Error converting or setting field '{}' in entity '{}': {}", fieldName, entityName, e.getMessage(), e);
                    throw new CustomException("Error updating field '" + fieldName + "' in entity " + entityName, e);
                }
            }

            entityManager.merge(entity);
            entityManager.flush();

            log.info("Successfully completed partial update for entity: {}, ID: {}", entityName, id);
            return convertEntityToMap(entity);

        } catch (Exception e) {
            log.error(" Error during partial update for entity: {}, ID: {}: {}", entityName, id, e.getMessage(), e);
            throw new CustomException("Partial update failed for " + entityName + " with ID " + id, e);
        }
    }


    @Transactional
    public List<Map<String, Object>> executeNativeQuery(QueryRequestDto queryRequestDto) {
        QueryMaster queryMaster = (QueryMaster) getEntityById("QueryMaster",queryRequestDto.getQueryId());
        try {
            String queryString = queryMaster.getQuery();
            String tenantSchema = queryRequestDto.getTenantName(); // Assuming tenant schema is passed in the request
            if (tenantSchema !=null) {
                // Replace placeholders in the query with the tenant schema
                queryString = queryString.replace("{schema}", tenantSchema);
            }
            log.info("Executing native query: {}", queryRequestDto.getQueryId());
            Query query = entityManager.createNativeQuery(queryString, Tuple.class);

            if (queryRequestDto.getParameters() != null) {
                queryRequestDto.getParameters().forEach(query::setParameter);
            }
            List<Tuple> resultList = query.getResultList();
            return mapTupleResultList(resultList);
        }
        catch (Exception e) {
            log.error("Error executing native query for QueryId {}: {}", queryRequestDto.getQueryId(), e.getMessage(), e);
            throw new CustomException(e);
        }
    }




    private List<Map<String, Object>> mapTupleResultList(List<Tuple> resultList) {
        List<Map<String, Object>> finalResult = new ArrayList<>();
        for (Tuple tuple : resultList) {
            Map<String, Object> row = new HashMap<>();
            for (TupleElement<?> element : tuple.getElements()) {
                String alias = element.getAlias();
                if (alias != null) {
                    alias = convertToCamelCase(alias);
                }

                // Get the value
                Object value = tuple.get(element);

                // Handle JSON string values
                if (value instanceof String) {
                    String strValue = (String) value;
                    if (isLikelyJson(strValue)) {
                        try {
                            // Parse JSON string to appropriate object
                            value = objectMapper.readValue(strValue, Object.class);
                            log.debug("Successfully parsed JSON for field: {}", alias);
                        } catch (JsonProcessingException e) {
                            log.debug("Failed to parse JSON for field: {}, error: {}", alias, e.getMessage());
                            // Keep original value if parsing fails
                        }
                    }
                }

                row.put(alias, value);
            }
            finalResult.add(row);
        }
        return finalResult;
    }

    /**
     * Checks if a string value is likely to be JSON
     */
    private boolean isLikelyJson(String value) {
        if (value == null || value.isEmpty()) {
            return false;
        }

        String trimmed = value.trim();
        // Check if it starts with { and ends with } (JSON object)
        // or starts with [ and ends with ] (JSON array)
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"));
    }

    /**
     * Converts a snake_case string to camelCase
     * For example: "product_id" becomes "productId"
     */
    private String convertToCamelCase(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }

        // Split the string by underscores
        String[] parts = snakeCase.split("_");
        if (parts.length == 0) {
            return snakeCase;
        }

        // Start with the first part in lowercase
        StringBuilder camelCase = new StringBuilder(parts[0].toLowerCase());

        // Capitalize the first letter of each remaining part
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i];
            if (part != null && !part.isEmpty()) {
                camelCase.append(Character.toUpperCase(part.charAt(0)));
                if (part.length() > 1) {
                    camelCase.append(part.substring(1).toLowerCase());
                }
            }
        }

        return camelCase.toString();
    }
//    @Transactional(readOnly = true)
//    public List<Map<String, Object>> getEntitiesByField(String entityName, String fieldName, String fieldValue) {
//        try {
//            log.info("Getting entities for {}, where {} = {}", entityName, fieldName, fieldValue);
//
//            Class<?> entityClass = getEntityClass(entityName);
//
//            // Build query
//            String queryStr = "SELECT e FROM " + entityClass.getSimpleName() + " e WHERE e." + fieldName + " = :fieldValue";
//            TypedQuery<?> query = entityManager.createQuery(queryStr, entityClass);
//
//            // Reflectively get field type
//            Field field = entityClass.getDeclaredField(fieldName);
//            field.setAccessible(true);
//            Class<?> fieldType = field.getType();
//
//            // Handle entity associations
//            if (!fieldType.isPrimitive() && !fieldType.getName().startsWith("java.lang")) {
//                // Assume field is an entity reference
//                Object associatedEntity = getAssociatedEntity(fieldType, fieldName, fieldValue);
//                query.setParameter("fieldValue", associatedEntity);
//            } else {
//                // Convert string value to actual field type
//                Object typedValue = convertToFieldType(fieldValue, fieldType);
//                query.setParameter("fieldValue", typedValue);
//            }
//
//            // Execute and convert result to Map
//            return query.getResultList().stream()
//                    .map(this::convertEntityToMap)
//                    .collect(Collectors.toList());
//
//        } catch (NoSuchFieldException nsfe) {
//            log.error("Field '{}' not found in entity '{}'", fieldName, entityName, nsfe);
//            throw new CustomException("Field not found: " + fieldName, nsfe);
//        } catch (Exception e) {
//            log.error("Error retrieving entities by field: {}", e.getMessage(), e);
//            throw new CustomException(e);
//        }
//    }


    @Transactional(readOnly = true)
    public List<Map<String, Object>> getEntitiesByField(String entityName, String fieldName, String fieldValue) {
        try {
            log.info("Getting entities for {}, where {} = {}", entityName, fieldName, fieldValue);

            Class<?> entityClass = getEntityClass(entityName);

            Field field = entityClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            Class<?> fieldType = field.getType();

            boolean isEntityReference = !fieldType.isPrimitive() && !fieldType.getName().startsWith("java.lang");

            String nestedFieldName = "id"; // default

            if (isEntityReference) {
                JoinColumn joinColumn = field.getAnnotation(JoinColumn.class);
                String dbColumn = joinColumn != null ? joinColumn.referencedColumnName() : "id";
                nestedFieldName = resolveTargetFieldName(fieldType, dbColumn);
            }

            String queryStr = isEntityReference
                    ? String.format("SELECT e FROM %s e WHERE e.%s.%s = :fieldValue",
                    entityClass.getSimpleName(), fieldName, nestedFieldName)
                    : String.format("SELECT e FROM %s e WHERE e.%s = :fieldValue",
                    entityClass.getSimpleName(), fieldName);

            TypedQuery<?> query = entityManager.createQuery(queryStr, entityClass);

            Object typedValue = convertToFieldType(
                    fieldValue,
                    isEntityReference ? getFieldType(fieldType, nestedFieldName) : field.getType()
            );

            query.setParameter("fieldValue", typedValue);

            return query.getResultList().stream()
                    .map(this::convertEntityToMap)
                    .collect(Collectors.toList());

        } catch (NoSuchFieldException nsfe) {
            log.error("Field '{}' not found in entity '{}'", fieldName, entityName, nsfe);
            throw new CustomException("Field not found: " + fieldName, nsfe);
        } catch (Exception e) {
            log.error("Error retrieving entities by field: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }

    private String resolveTargetFieldName(Class<?> referencedClass, String dbColumnName) {
        for (Field f : referencedClass.getDeclaredFields()) {
            Column col = f.getAnnotation(Column.class);
            if (col != null && col.name().equalsIgnoreCase(dbColumnName)) {
                return f.getName(); // return the Java field name
            }
        }
        return "id"; // fallback if not matched
    }


    private Class<?> getFieldType(Class<?> entityClass, String fieldName) throws NoSuchFieldException {
        Field nestedField = entityClass.getDeclaredField(fieldName);
        nestedField.setAccessible(true);
        return nestedField.getType();
    }


    @Transactional(readOnly = true)
    public List<Map<String, Object>> getEntitiesSorted(String entityName, String sortBy, String sortOrder) {
        try {
            log.info("Getting sorted entities of {}, by {} {}", entityName, sortBy, sortOrder);

            Class<?> entityClass = getEntityClass(entityName);
            String direction = sortOrder.equalsIgnoreCase("desc") ? "DESC" : "ASC";

            // Get field type
            Field sortField = entityClass.getDeclaredField(sortBy);
            sortField.setAccessible(true);
            Class<?> fieldType = sortField.getType();

            // Decide if it's an association or a basic field
            boolean isAssociation = !fieldType.isPrimitive()
                    && !fieldType.getName().startsWith("java.lang")
                    && !fieldType.isEnum();

            String orderByClause;
            if (isAssociation) {
                // Sort by ID or key field in associated entity
                // Assumes key field is named "id" — adjust if needed
                orderByClause = "e." + sortBy + ".id " + direction;
            } else {
                // Sort by simple field
                orderByClause = "e." + sortBy + " " + direction;
            }

            String queryStr = "SELECT e FROM " + entityClass.getSimpleName() + " e ORDER BY " + orderByClause;
            List<?> results = entityManager.createQuery(queryStr, entityClass).getResultList();

            return results.stream()
                    .map(this::convertEntityToMap)
                    .collect(Collectors.toList());

        } catch (NoSuchFieldException nsfe) {
            log.error("Sort field '{}' not found in entity '{}'", sortBy, entityName, nsfe);
            throw new CustomException("Sort field not found: " + sortBy, nsfe);
        } catch (Exception e) {
            log.error("Error retrieving sorted entities: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }


    private Object convertToFieldType(String value, Class<?> fieldType) {
        if (fieldType.equals(Boolean.class) || fieldType.equals(boolean.class)) {
            return Boolean.parseBoolean(value);
        } else if (fieldType.equals(Integer.class) || fieldType.equals(int.class)) {
            return Integer.parseInt(value);
        } else if (fieldType.equals(Long.class) || fieldType.equals(long.class)) {
            return Long.parseLong(value);
        } else if (fieldType.equals(Double.class) || fieldType.equals(double.class)) {
            return Double.parseDouble(value);
        } else if (fieldType.equals(Float.class) || fieldType.equals(float.class)) {
            return Float.parseFloat(value);
        } else if (fieldType.equals(String.class)) {
            return value;
        } else if (fieldType.equals(Date.class)) {
            try {
                return new SimpleDateFormat("yyyy-MM-dd").parse(value);
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid java.util.Date format. Expected yyyy-MM-dd: " + value);
            }
        } else if (fieldType.equals(java.sql.Date.class)) {
            return java.sql.Date.valueOf(value); // expects yyyy-[m]m-[d]d
        } else if (fieldType.equals(LocalDate.class)) {
            return LocalDate.parse(value, DateTimeFormatter.ISO_LOCAL_DATE); // yyyy-MM-dd
        } else if (fieldType.equals(LocalDateTime.class)) {
            return LocalDateTime.parse(value, DateTimeFormatter.ISO_LOCAL_DATE_TIME); // yyyy-MM-ddTHH:mm:ss
        } else if (fieldType.equals(ZonedDateTime.class)) {
            return ZonedDateTime.parse(value, DateTimeFormatter.ISO_ZONED_DATE_TIME); // yyyy-MM-ddTHH:mm:ssZ[Zone]
        } else if (fieldType.equals(OffsetDateTime.class)) {
            return OffsetDateTime.parse(value, DateTimeFormatter.ISO_OFFSET_DATE_TIME); // yyyy-MM-ddTHH:mm:ss+hh:mm
        } else if (fieldType.equals(Timestamp.class)) {
            return Timestamp.valueOf(value); // expects yyyy-MM-dd HH:mm:ss[.f...]
        } else {
            throw new IllegalArgumentException("Unsupported field type: " + fieldType.getName());
        }
    }



    private Object getAssociatedEntity(Class<?> associatedClass, String keyField, String keyValue) {
        try {
            // Assumes the field name is same as the identifier field in the associated entity
            String jpql = "SELECT e FROM " + associatedClass.getSimpleName() + " e WHERE e." + keyField + " = :keyValue";
            TypedQuery<?> query = entityManager.createQuery(jpql, associatedClass);
            query.setParameter("keyValue", keyValue);
            return query.getSingleResult();
        } catch (NoResultException e) {
            throw new CustomException("Associated entity not found for value: " + keyValue, e);
        }
    }


    @Transactional
    public Map<String, Object> saveBulkEntity(String entityName, Map<String, Object> entityData) throws FeignClientException {
        log.info("Saving bulk entity: {}", entityName);
        try {
            Class<?> entityClass = getEntityClass(entityName);
            log.debug("Resolved class for '{}': {}", entityName, entityClass.getName());

            entityData = processNestedEntities(entityData,entityClass);

            Object entity = createEntityFromMap(entityClass, entityData);
            log.debug("Created entity from map for '{}': {}", entityName, entity);

            entityManager.persist(entity);
            entityManager.flush();
            log.info("Entity persisted: {}", entityName);

            encryptionEntityListener.decrypt(entity);
            log.debug("Decrypted entity: {}", entity);

            Map<String, Object> resultMap = convertEntityToMap(entity);
            log.debug("Converted entity to map: {}", resultMap);
            return resultMap;

        } catch (Exception e) {
            log.error("Error saving entity '{}': {}", entityName, e.getMessage(), e);
            throw new CustomException(e);
        }
    }

    @Transactional(readOnly = true)
    public Map<String, Object> getEntityById(String entityName, Long id) {
        try {
            log.info("Fetching {} by ID: {}", entityName, id);
            Class<?> entityClass = getEntityClass(entityName);
            Object entity = entityManager.find(entityClass, id);
            if (entity == null) {
                throw new EntityNotFoundException(entityName + " with ID " + id + " not found");
            }
            return convertEntityToMap(entity);
        } catch (Exception e) {
            log.error("Error retrieving entity: {}", e.getMessage(), e);
            throw new CustomException(e);
        }
    }



    public Map<String, Object> processNestedEntities(Map<String, Object> jsonBody, Class<?> entityClass) throws FeignClientException {
        log.debug("Processing nested entities for payload: {}", jsonBody.keySet());
        for (Map.Entry<String, Object> entry : new HashMap<>(jsonBody).entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map<?, ?> nestedMapRaw) {
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) nestedMapRaw;
                Field field = null;
                try {
                    field = entityClass.getDeclaredField(key);
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                }
                Class<?> fieldType = field.getType();
                processNestedEntity(jsonBody, key, nestedMap,primitiveFieldType(fieldType));
            }
        }

        log.debug("Finished processing nested entities.");
        return jsonBody;
    }

    private void processNestedEntity(Map<String, Object> jsonBody, String key, Map<String, Object> nestedMap, boolean updateWithId)  {
        if (nestedMap.containsKey("id")) {
            log.debug("Skipping nested entity '{}' as 'id' is already present: {}", key, nestedMap.get("id"));
            return;
        }
        String entityName = (String) nestedMap.get("entityName");

        try {
            Map<String, Object> cleanMap = new HashMap<>(nestedMap);

            if (entityName != null) {
                cleanMap.remove("entityName");
                log.info("Processing nested entity for key='{}' with entityName='{}'", key, entityName);

                Map<String, Object> savedEntityMap = saveEntity(entityName, cleanMap);
                Object savedId = savedEntityMap.get("id");
                log.info("SAVED ID : "+ savedId);
                log.info("Update With ID  : "+ savedId);
                if (updateWithId) {
                    jsonBody.put(key, savedId);
                    log.info("JSON BODY : "+ jsonBody);
                }
                else {
                    jsonBody.put(key, savedEntityMap);
                    log.info("Successfully saved nested entity '{}'. Returned ID: {}", key, savedId);
                }
            }
        } catch (Exception ex) {
            log.error("Error while processing nested entity for key='{}'. Message: {}", key, ex.getMessage(), ex);
            throw new CustomException(ex);
        }
    }

    private boolean primitiveFieldType(Class<?> clazz) {
        boolean isPrimitive = clazz.isPrimitive()
                || clazz == Long.class
                || clazz == Integer.class
                || clazz == Double.class
                || clazz == Float.class
                || clazz == Boolean.class
                || clazz == Short.class
                || clazz == Byte.class
                || clazz == Character.class
                || clazz == String.class;
        log.debug("Checking if class '{}' is primitive or wrapper: {}", clazz.getName(), isPrimitive);
        return isPrimitive;
    }
}
