package com.rbts.repository;

import com.rbts.entity.auth.Passwords;
import com.rbts.entity.auth.Users;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PasswordsRepository extends JpaRepository<Passwords, Long> {

    List<Passwords> findTop5ByUserOrderByCreatedDateDesc(Users user);

    List<Passwords> findTop5ByUserIdOrderByCreatedDateDesc(Long id);

    Passwords findByUserId(Long id);

    @Transactional
    @Modifying
    @Query("DELETE FROM Passwords p WHERE p.user.id = :id")
    void deleteByUserId(@Param("id") Long userId);
}
