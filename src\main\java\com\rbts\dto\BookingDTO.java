package com.rbts.dto;
import jakarta.validation.constraints.*;
import lombok.*;
import java.time.LocalDateTime;

@Data
@Builder
public class BookingDTO {
    private Long id;
    @NotNull(message = "Session ID is required")
    private Long sessionId;
    @NotNull(message = "Student ID is required")
    private Long studentId;
    @NotNull(message = "Start time is required")
    private LocalDateTime startTime;
    @NotNull(message = "End time is required")
    private LocalDateTime endTime;
    @Min(value = 0, message = "Credit points cannot be negative")
    private Integer creditPointsUsed;
    private String calendlyUri;
}