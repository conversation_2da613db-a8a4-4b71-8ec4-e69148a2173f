package com.rbts.controller.auth;

import com.rbts.entity.auth.ClientCustomGroup;
import com.rbts.service.auth.ClientCustomGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user/client-custom-groups")
@RequiredArgsConstructor
public class ClientCustomGroupController {

    private final ClientCustomGroupService service;
    @PostMapping("/save")
    public ResponseEntity<ClientCustomGroup> create(@RequestBody ClientCustomGroup clientCustomGroup) {
        return ResponseEntity.ok(service.create(clientCustomGroup));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ClientCustomGroup> update(@PathVariable Long id, @RequestBody ClientCustomGroup clientCustomGroup) {
        return ResponseEntity.ok(service.update(id, clientCustomGroup));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<ClientCustomGroup> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/getAll")
    public ResponseEntity<List<ClientCustomGroup>> getAll() {
        return ResponseEntity.ok(service.getAll());
    }
}
