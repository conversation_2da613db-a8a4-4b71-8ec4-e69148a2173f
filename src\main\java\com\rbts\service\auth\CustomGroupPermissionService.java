package com.rbts.service.auth;

import com.rbts.entity.auth.ClientCustomGroup;
import com.rbts.entity.auth.CustomGroupPermission;
import com.rbts.repository.ClientCustomGroupRepository;
import com.rbts.repository.CustomGroupPermissionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CustomGroupPermissionService {

    private final CustomGroupPermissionRepository repository;
    private final ClientCustomGroupRepository clientGroupRepo;

    public CustomGroupPermission create(CustomGroupPermission permission) {
        if (permission.getCustomGroupKey() == null || permission.getCustomGroupKey().getGroupKey() == null) {
            throw new RuntimeException("Custom group key is required.");
        }

        ClientCustomGroup group = clientGroupRepo.findByGroupKey(permission.getCustomGroupKey().getGroupKey())
                .orElseThrow(() -> new RuntimeException("Group key not found"));

        permission.setCustomGroupKey(group);
        permission.setPermissionKeyIdJson(SetToJsonConverter.convertToDatabaseColumn(permission.getPermissionKeySet()));

        return repository.save(permission);
    }

    public CustomGroupPermission update(Long id, CustomGroupPermission updated) {
        CustomGroupPermission existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("CustomGroupPermission not found"));

        ClientCustomGroup group = clientGroupRepo.findByGroupKey(updated.getCustomGroupKey().getGroupKey())
                .orElseThrow(() -> new RuntimeException("Group key not found"));

        existing.setCustomGroupKey(group);
        existing.setIsActive(updated.getIsActive());
        existing.setPermissionKeySet(updated.getPermissionKeySet());

        return repository.save(existing);
    }

    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new RuntimeException("CustomGroupPermission not found");
        }
        repository.deleteById(id);
    }

    public Optional<CustomGroupPermission> getById(Long id) {
        return repository.findById(id);
    }

    public List<CustomGroupPermission> getAll() {
        return repository.findAll();
    }
}
