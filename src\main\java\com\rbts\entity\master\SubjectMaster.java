package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "subject_master")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubjectMaster {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "subjectSequenceGenerator")
    @SequenceGenerator(name = "subjectSequenceGenerator", sequenceName = "subject_sequence", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "subject_key", nullable = false, unique = true)
    private String subjectKey;

    @NotNull
    @Column(name = "subject_short_name", nullable = false)
    private String subjectShortName;

    @Column(name = "subject_description", columnDefinition = "TEXT")
    private String subjectDescription;
}
