package com.rbts.service.auth;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class SetToJsonConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // Convert Set<String> to JSON String
    public static String convertToDatabaseColumn(Set<String> attribute) {
        if (attribute == null || attribute.isEmpty()) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert Set<String> to JSON string.", e);
        }
    }

    // Convert JSON String to Set<String>
    public static Set<String> convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return new HashSet<>();
        }
        try {
            return objectMapper.readValue(dbData, new TypeReference<Set<String>>() {});
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert JSON string to Set<String>.", e);
        }
    }
}
