package com.rbts.entity.master;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "session_types")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionType {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sessionTypeSeqGen")
    @SequenceGenerator(name = "sessionTypeSeqGen", sequenceName = "session_type_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "session_type_key", nullable = false, unique = true)
    private String sessionTypeKey;

    @Column(name = "session_type_desc")
    private String sessionTypeDesc;
}
