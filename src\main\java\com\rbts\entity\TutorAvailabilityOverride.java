package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(
        name = "tutor_availability_overrides",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"contact_id", "override_date", "start_time"})
        }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorAvailabilityOverride {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorAvailabilityOverrideSeqGen")
    @SequenceGenerator(name = "tutorAvailabilityOverrideSeqGen", sequenceName = "tutor_availability_override_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails contact;

    @NotNull
    @Column(name = "override_date",nullable = false)
    private Long overrideDate;

    @Column(name = "start_time",nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time",nullable = false)
    private LocalDateTime endTime;

    @NotNull
    @Column(name = "is_available",nullable = false)
    private Boolean isAvailable;


}
