package com.rbts.controller.auth;

import com.rbts.entity.auth.UserPermission;
import com.rbts.service.auth.UserPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api/user/user-permissions")
@RequiredArgsConstructor
public class UserPermissionController {

    private final UserPermissionService service;

    @PostMapping("/save")
    public ResponseEntity<UserPermission> create(@RequestBody UserPermission userPermission) {
        return ResponseEntity.ok(service.create(userPermission));
    }

    @PutMapping("/{id}")
    public ResponseEntity<UserPermission> update(@PathVariable Long id, @RequestBody UserPermission userPermission) {
        return ResponseEntity.ok(service.update(id, userPermission));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserPermission> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/getAll")
    public ResponseEntity<List<UserPermission>> getAll() {
        return ResponseEntity.ok(service.getAll());
    }
  @GetMapping("/getAllPermission/{userId}")
    public ResponseEntity<Set<String>> getAllPermission(@PathVariable Long userId) {
        return ResponseEntity.ok(service.getAllPermission(userId));
    }

}
