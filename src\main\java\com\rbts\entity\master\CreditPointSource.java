package com.rbts.entity.master;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "credit_point_source", uniqueConstraints = {
        @UniqueConstraint(columnNames = "source_key")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreditPointSource {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "creditPointSourceSeqGen")
    @SequenceGenerator(name = "creditPointSourceSeqGen", sequenceName = "credit_point_source_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "source_key", nullable = false, unique = true)
    private String sourceKey;

    @Column(name = "source_desc")
    private String sourceDesc;
}
