package com.rbts.repository;

import com.rbts.entity.Booking;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<Booking, Long> {

    List<Booking> findBySession(Session session);
    
    List<Booking> findByStudents(ContactDetails student);
    
    @Query("SELECT b FROM Booking b WHERE b.session.tutorId = :tutor")
    List<Booking> findByTutor(@Param("tutor") ContactDetails tutor);
    
    @Query("SELECT b FROM Booking b WHERE b.bookedAt BETWEEN :startDate AND :endDate")
    List<Booking> findByBookedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT b FROM Booking b WHERE b.session = :session AND b.students = :student")
    Optional<Booking> findBySessionAndStudent(@Param("session") Session session, @Param("student") ContactDetails student);
    
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.session = :session")
    Long countBySession(@Param("session") Session session);
    
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.students = :student")
    Long countByStudent(@Param("student") ContactDetails student);
}
