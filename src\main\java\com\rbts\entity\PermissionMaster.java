package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "permission_master")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionMaster extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "permissionmastersequencegenerator")
    @GenericGenerator(
            name = "permissionmastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "permissionmastersequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "permission_name", nullable = false, length = 50, unique = true)
    private String permissionName;

    @NotNull
    @Column(name = "permission_key", nullable = false, length = 50, unique = true)
    private String permissionKey;

    @NotNull
    @Column(name = "permission_desc", nullable = false, columnDefinition = "TEXT")
    private String permissionDesc;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "group_type_key", referencedColumnName = "group_type_key", nullable = false)
    private PermissionGroupType groupTypeKey;
}
