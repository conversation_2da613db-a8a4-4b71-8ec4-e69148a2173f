package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "tutor_verification_calls")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorVerificationCall {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorVerificationSeqGen")
    @SequenceGenerator(name = "tutorVerificationSeqGen", sequenceName = "tutor_verification_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails contact;

    @NotNull
    @Column(name = "scheduled_at", nullable = false)
    private LocalDateTime scheduledAt;

    @Column(name = "meeting_url", columnDefinition = "TEXT")
    private String meetingUrl;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "status_id", nullable = false)
    private StatusMaster status;

    @Column(name = "admin_notes", columnDefinition = "TEXT")
    private String adminNotes;

    @Column(name = "verification_call_recording_url")
    private String verificationCallRecordingUrl;
}
