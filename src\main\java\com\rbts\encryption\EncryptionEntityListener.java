package com.rbts.encryption;

import jakarta.persistence.PostLoad;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import java.lang.reflect.Field;

public class EncryptionEntityListener {
    private final Base64EncryptionUtils encryptionUtils = new Base64EncryptionUtils();

    private static final String ENCRYPTION_PREFIX = "ENC:"; // Prefix to identify encrypted values

    @PrePersist
    @PreUpdate
    public void encrypt(Object entity) {
        processEntity(entity, true);
    }

    @PostLoad
    public void decrypt(Object entity) {
        processEntity(entity, false);
    }

    private void processEntity(Object entity, boolean encrypt) {
        for (Field field : entity.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(Encrypt.class)) {
                field.setAccessible(true);
                try {
                    String value = (String) field.get(entity);
                    if (value != null) {
                        String processedValue = encrypt
                                ? encryptValue(value)
                                : decryptValue(value);
                        field.set(entity, processedValue);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to process field " + field.getName(), e);
                }
            }
        }
    }

    private String encryptValue(String value) throws Exception {
        if (value.startsWith(ENCRYPTION_PREFIX)) {
            return value;
        }
        return  encryptionUtils.encrypt(value);
    }

    private String decryptValue(String value) throws Exception {
        if (value.startsWith(ENCRYPTION_PREFIX)) {
            // Remove the prefix and decrypt
               return encryptionUtils.decrypt(value);
        }
        // Already plain text, return as is
        return value;
    }
}