package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "tutor_qualification_and_documents")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorQualificationAndDocuments {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorQualificationSeqGen")
    @SequenceGenerator(name = "tutorQualificationSeqGen", sequenceName = "tutor_qualification_sequence", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails contact;

    @Column(name = "qualification_type", length = 100)
    private String qualificationType;

    @Column(name = "qualification_field", length = 100)
    private String qualificationField;

    @Column(name = "university_name", length = 150)
    private String universityName;

    @Column(name = "course_name", length = 150)
    private String courseName;

    @Column(name = "qualification_date")
    private Date qualificationDate;

    @Column(name = "grade_achieved", length = 50)
    private String gradeAchieved;

    @Column(name = "certificate_url", columnDefinition = "TEXT")
    private String certificateUrl;
}
