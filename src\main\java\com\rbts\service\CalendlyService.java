package com.rbts.service;

import com.rbts.dto.SessionDTO;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import com.rbts.repository.ContactDetailsRepository;
import com.rbts.repository.SessionRepository;
import com.rbts.repository.CalendlyOrganizationMemberDetailsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CalendlyService {

    @Autowired
    private SessionRepository sessionRepository;

    @Autowired
    private ContactDetailsRepository contactDetailsRepository;

    @Autowired
    private CalendlyOrganizationMemberDetailsRepository calendlyMemberRepository;

    @Autowired
    private RestTemplate calendlyRestTemplate;

    @Value("${calendly.api.url}")
    private String calendlyApiUrl;

    @Autowired
    private CalendlyOrganizationMemberDetailsRepository calendlyMemberRepository;

    @Autowired
    private ContactDetailsRepository contactDetailsRepository;

    @Value("${calendly.organization-uuid}")
    private String organizationUuid;

    @Transactional
    public SessionDTO createEventType(SessionDTO sessionDTO) {
        ContactDetails tutor = contactDetailsRepository.findById(sessionDTO.getTutorId())
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        String userUri = calendlyMemberRepository.findByUserEmail(tutor.getEmailId())
                .map(com.rbts.entity.CalendlyOrganizationMemberDetails::getUserUri)
                .orElseThrow(() -> new RuntimeException("Calendly user not found"));

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", sessionDTO.getTitle());
        requestBody.put("duration", calculateDuration(sessionDTO.getStartTime(), sessionDTO.getEndTime()));
        requestBody.put("kind", "solo");
        requestBody.put("owner", userUri);
        requestBody.put("active", true);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, getHeaders());
        ResponseEntity<Map> response = calendlyRestTemplate.postForEntity(
                calendlyApiUrl + "/event_types", request, Map.class);

        Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");
        Session session = mapToSession(sessionDTO);
        session.setEventTypeUri((String) resource.get("uri"));
        session.setSchedulingUrl((String) resource.get("scheduling_url"));
        session.setActive(true);

        session = sessionRepository.save(session);
        return mapToSessionDTO(session);
    }

    public SessionDTO getEventType(Long id) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found"));
        return mapToSessionDTO(session);
    }

    public List<SessionDTO> getTutorEventTypes(Long tutorId) {
        ContactDetails tutor = contactDetailsRepository.findById(tutorId)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));
        return sessionRepository.findByTutorId(tutor).stream()
                .map(this::mapToSessionDTO)
                .collect(Collectors.toList());
    }

    @Transactional
    public SessionDTO updateEventType(Long id, SessionDTO sessionDTO) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found"));

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", sessionDTO.getTitle());
        requestBody.put("duration", calculateDuration(sessionDTO.getStartTime(), sessionDTO.getEndTime()));
        requestBody.put("kind", "solo");
        requestBody.put("active", sessionDTO.isActive());

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, getHeaders());
        calendlyRestTemplate.exchange(session.getEventTypeUri(), HttpMethod.PUT, request, Map.class);

        updateSessionFromDTO(session, sessionDTO);
        session = sessionRepository.save(session);
        return mapToSessionDTO(session);
    }

    @Transactional
    public void deleteEventType(Long id) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found"));
        if (session.getEventTypeUri() != null) {
            calendlyRestTemplate.exchange(session.getEventTypeUri(), HttpMethod.DELETE,
                    new HttpEntity<>(getHeaders()), Void.class);
        }
        sessionRepository.delete(session);
    }

    private Integer calculateDuration(java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return startTime != null && endTime != null ?
                (int) java.time.Duration.between(startTime, endTime).toMinutes() : 60;
    }

    private Session mapToSession(SessionDTO dto) {
        return Session.builder()
                .tutorId(contactDetailsRepository.findById(dto.getTutorId()).orElse(null))
                .title(dto.getTitle())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .active(dto.isActive())
                .build();
    }

    private SessionDTO mapToSessionDTO(Session session) {
        return SessionDTO.builder()
                .id(session.getId())
                .tutorId(session.getTutorId().getId())
                .title(session.getTitle())
                .startTime(session.getStartTime())
                .endTime(session.getEndTime())
                .eventTypeUri(session.getEventTypeUri())
                .schedulingUrl(session.getSchedulingUrl())
                .active(session.isActive())
                .build();
    }

    private void updateSessionFromDTO(Session session, SessionDTO dto) {
        if (dto.getTitle() != null) session.setTitle(dto.getTitle());
        if (dto.getStartTime() != null) session.setStartTime(dto.getStartTime());
        if (dto.getEndTime() != null) session.setEndTime(dto.getEndTime());
        session.setActive(dto.isActive());
    }

    private org.springframework.http.HttpHeaders getHeaders() {
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.set("Authorization", "Bearer " + getApiToken());
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        return headers;
    }

    private String getApiToken() {
        return systemConfigRepository.findByConfigKey("calendly.api.token")
                .map(SystemConfig::getConfigValue)
                .orElseThrow(() -> new RuntimeException("Calendly API token not found"));
    }

    public List<AvailabilityDTO> getTutorAvailability(Long tutorId, LocalDateTime startTime, LocalDateTime endTime) {
        ContactDetails tutor = contactDetailsRepository.findById(tutorId)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        List<Session> sessions = sessionRepository.findByTutorId(tutor);
        List<AvailabilityDTO> availability = new ArrayList<>();

        for (Session session : sessions) {
            if (session.getEventTypeUri() != null) {
                String url = String.format("%s/event_type_available_times?event_type=%s&start_time=%s&end_time=%s",
                        calendlyApiUrl, session.getEventTypeUri(),
                        startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

                HttpEntity<Void> request = new HttpEntity<>(getHeaders());
                ResponseEntity<Map> response = calendlyRestTemplate.exchange(url, org.springframework.http.HttpMethod.GET, request, Map.class);

                List<Map<String, Object>> slots = (List<Map<String, Object>>) response.getBody().get("collection");
                if (slots != null) {
                    availability.addAll(slots.stream().map(slot -> AvailabilityDTO.builder()
                            .startTime(LocalDateTime.parse((String) slot.get("start_time"), DateTimeFormatter.ISO_DATE_TIME))
                            .endTime(LocalDateTime.parse((String) slot.get("end_time"), DateTimeFormatter.ISO_DATE_TIME))
                            .tutorId(tutorId)
                            .timezone("Asia/Kolkata")
                            .status((String) slot.get("status"))
                            .build()).collect(Collectors.toList()));
                }
            }
        }
        return availability;
    }

    @Autowired
    private BookingRepository bookingRepository;

    @Autowired
    private BookingOccurrenceRepository bookingOccurrenceRepository;

    @Transactional
    public BookingDTO scheduleSession(BookingDTO bookingDTO) {
        Session session = sessionRepository.findById(bookingDTO.getSessionId())
                .orElseThrow(() -> new RuntimeException("Session not found"));
        ContactDetails student = contactDetailsRepository.findById(bookingDTO.getStudentId())
                .orElseThrow(() -> new RuntimeException("Student not found"));

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("event_type", session.getEventTypeUri());
        requestBody.put("start_time", bookingDTO.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        requestBody.put("end_time", bookingDTO.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        List<Map<String, String>> invitees = List.of(Map.of("email", student.getEmailId(), "name", student.getDisplayName()));
        requestBody.put("invitees", invitees);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, getHeaders());
        ResponseEntity<Map> response = calendlyRestTemplate.postForEntity(
                calendlyApiUrl + "/scheduled_events", request, Map.class);

        Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");

        Booking booking = Booking.builder()
                .session(session)
                .students(student)
                .bookedAt(LocalDateTime.now())
                .creditPointsUsed(bookingDTO.getCreditPointsUsed())
                .build();
        booking = bookingRepository.save(booking);

        BookingOccurrence occurrence = BookingOccurrence.builder()
                .booking(booking)
                .occurrenceTime(bookingDTO.getStartTime())
                .endTime(bookingDTO.getEndTime())
                .build();
        bookingOccurrenceRepository.save(occurrence);

        return BookingDTO.builder()
                .id(booking.getId())
                .sessionId(session.getId())
                .studentId(student.getId())
                .startTime(bookingDTO.getStartTime())
                .endTime(bookingDTO.getEndTime())
                .calendlyUri((String) resource.get("uri"))
                .build();
    }





        @Transactional
        public CalendlyOrganizationMemberDetails createOrganizationInvite(String email) {
            Map<String, Object> inviteRequest = new HashMap<>();
            inviteRequest.put("email", email);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(inviteRequest, getHeaders());
            ResponseEntity<Map> response = calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/organizations/" + organizationUuid + "/invitations", request, Map.class);

            Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");

            Optional<CalendlyOrganizationMemberDetails> existingMember = calendlyMemberRepository.findByUserEmail(email);
            CalendlyOrganizationMemberDetails member = existingMember.orElse(new CalendlyOrganizationMemberDetails());

            member.setOrganizationUri("https://api.calendly.com/organizations/" + organizationUuid);
            member.setUserEmail(email);
            member.setInvitationUri((String) resource.get("uri"));
            member.setInvitationStatus((String) resource.get("status"));
            member.setRole("user");
            member.setActive(true);

            Optional<ContactDetails> contact = contactDetailsRepository.findByEmailId(email);
            contact.ifPresent(member::setContact);

            return calendlyMemberRepository.save(member);
        }

        @Transactional
        public CalendlyOrganizationMemberDetails checkAndUpdateInviteStatus(String email) {
            CalendlyOrganizationMemberDetails member = calendlyMemberRepository.findByUserEmail(email)
                    .orElseThrow(() -> new RuntimeException("Member not found"));

            HttpEntity<Void> request = new HttpEntity<>(getHeaders());
            ResponseEntity<Map> response = calendlyRestTemplate.exchange(
                    member.getInvitationUri(), org.springframework.http.HttpMethod.GET, request, Map.class);

            Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");
            String currentStatus = (String) resource.get("status");

            if (!currentStatus.equals(member.getInvitationStatus())) {
                member.setInvitationStatus(currentStatus);
                if ("accepted".equals(currentStatus)) {
                    Map<String, Object> user = (Map<String, Object>) resource.get("user");
                    if (user != null) {
                        member.setUserUri((String) user.get("uri"));
                        member.setUserName((String) user.get("name"));
                        member.setSchedulingUrl((String) user.get("scheduling_url"));
                        member.setTimezone((String) user.get("timezone"));
                        member.setLastAvailabilitySync(LocalDateTime.now());
                    }
                }
                member = calendlyMemberRepository.save(member);
            }
            return member;
        }
    }

}