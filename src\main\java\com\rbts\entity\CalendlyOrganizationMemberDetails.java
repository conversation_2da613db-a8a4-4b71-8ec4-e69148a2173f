package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "calendly_organization_member_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyOrganizationMemberDetails extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "calendlyOrgMemberSeqGen")
    @SequenceGenerator(name = "calendlyOrgMemberSeqGen", sequenceName = "calendly_org_member_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "organization_uri")
    private String organizationUri;

    @Column(name = "role")
    private String role;

    @Column(name = "invitation_uri", columnDefinition = "TEXT")
    private String invitationUri;

    @Column(name = "invitation_status")
    private String invitationStatus; // pending, accepted, declined

    @Column(name = "user_uri", unique = true)
    private String userUri;

    @Column(name = "user_email", unique = true)
    private String userEmail;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "scheduling_url", columnDefinition = "TEXT")
    private String schedulingUrl;

    @Column(name = "slug", unique = true)
    private String slug;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "locale")
    private String locale;

    @Column(name = "time_notation")
    private String timeNotation;

    @Column(name = "active")
    private boolean active;

    // Calendly Availability Fields
    @Column(name = "availability_schedule_uri", columnDefinition = "TEXT")
    private String availabilityScheduleUri;

    @Column(name = "default_schedule_uri", columnDefinition = "TEXT")
    private String defaultScheduleUri;

    @Column(name = "availability_rules", columnDefinition = "TEXT")
    private String availabilityRules; // JSON string of availability rules

    @Column(name = "buffer_time_before")
    private Integer bufferTimeBefore; // in minutes

    @Column(name = "buffer_time_after")
    private Integer bufferTimeAfter; // in minutes

    @Column(name = "minimum_scheduling_notice")
    private Integer minimumSchedulingNotice; // in minutes

    @Column(name = "maximum_events_per_day")
    private Integer maximumEventsPerDay;

    @Column(name = "date_overrides", columnDefinition = "TEXT")
    private String dateOverrides; // JSON string of date-specific overrides

    @Column(name = "last_availability_sync")
    private LocalDateTime lastAvailabilitySync;

    @OneToOne
    @JoinColumn(name = "contact_id")
    private ContactDetails contact;
}
