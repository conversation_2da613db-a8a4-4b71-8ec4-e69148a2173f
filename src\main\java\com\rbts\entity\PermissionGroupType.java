package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "permission_groups_types")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PermissionGroupType extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "permissiongrouptypequencegenerator")
    @GenericGenerator(
            name = "permissiongrouptypequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "permissiongrouptypequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "group_type_key", nullable = false, unique = true)
    private String groupTypeKey;

    @NotNull
    @Column(name = "group_type_name", nullable = false)
    private String groupTypeName;

    @NotNull
    @Column(name = "group_type_desc",nullable = false)
    private String groupTypeDesc;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @NotNull
    @Column(name = "is_god_mode", nullable = false)
    private Boolean isGodMode;
}
