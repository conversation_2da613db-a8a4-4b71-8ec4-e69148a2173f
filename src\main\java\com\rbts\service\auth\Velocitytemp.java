package com.rbts.service.auth;

import com.rbts.dto.NotificationDto;
import com.rbts.entity.auth.Templates;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.resource.loader.StringResourceLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;

@Service
@Slf4j
@Data
public class Velocitytemp {

    @Autowired
    private VelocityEngine velocityEngine;

    @Autowired
    private TemplatesService templatesService;

    public String generateTemplate2(NotificationDto notificationDto, Templates templates) throws IOException {
        log.debug("velocityTemplate notificationDto:: {}", notificationDto);

        String templateName = "template";
        String templateContent = templates.getTemplateHeader() + templates.getTemplateBody() + templates.getTemplateFooter();

        StringResourceLoader.getRepository().putStringResource(templateName, templateContent);

        Template template = velocityEngine.getTemplate(templateName);

        VelocityContext context = new VelocityContext();
        context.put("name", notificationDto.getFirstName() + " " + notificationDto.getLastName());
        context.put("username", notificationDto.getUsername());
        context.put("password", notificationDto.getPassword());
        context.put("otp", notificationDto.getOtp());
        context.put("companyName", notificationDto.getCompanyName());

        return contextWriter(template, context);
    }

    public String contextWriter(Template template, VelocityContext context) {
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        return writer.toString();
    }
}