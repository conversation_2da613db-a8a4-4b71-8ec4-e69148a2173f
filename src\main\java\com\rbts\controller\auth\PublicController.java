package com.rbts.controller.auth;


import com.rbts.exception.DuplicateDataException;
import com.rbts.exception.NullPointerException;
import com.rbts.dto.LoginRequest;
import com.rbts.dto.SignupRequest;
import com.rbts.exception.ResourceNotFoundException;
import com.rbts.service.auth.LogoutService;
import com.rbts.service.auth.PasswordsService;
import com.rbts.service.auth.SignupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/user")

@Slf4j
public class PublicController  {

    @Autowired
    SignupService signupService;

    @Autowired
    PasswordsService passwordsService;

    @Autowired
    LogoutService logoutService;


    /**
     * Registers a new user.
     *
     * @param signUpRequest The signup request object containing user details.
     * @return ResponseEntity representing the response of the registration process.
     */

    @Transactional
    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@RequestBody SignupRequest signUpRequest) throws Exception {
            return signupService.save(signUpRequest);
    }
    /**
     * Authenticates a user with the provided username and password.
     *
     * @param loginRequest The LoginRequest object containing the username and password.
//     * @param       The HttpServletRequest object.
     * @return ResponseEntity representing the response of the authentication process.
     */

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@RequestBody LoginRequest loginRequest) throws Exception {
        return signupService.signIn(loginRequest);
    }
    /**
     * Logs out the currently authenticated user.
     *
     * @param request The HttpServletRequest object.
     * @return ResponseEntity representing the response of the logout process.
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout(HttpServletRequest request) throws Exception {
       return logoutService.logout(request);
    }

    @GetMapping("/validateUsername/{username}")
    public ResponseEntity<String> validateUsername(@PathVariable  String username) throws Exception {
        try {
            return signupService.validateUsername(username);
        } catch (DuplicateDataException e) {
            return ResponseEntity.status(701).body(e.getMessage());
        } catch (NullPointerException e) {
            return ResponseEntity.status(404).body(e.getMessage());
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(304).body(e.getMessage());
        }
    }


    @GetMapping("/validateEmail/{email}/{name}")
    public ResponseEntity<String> validEmail(@PathVariable(name = "email") String email, @PathVariable(name = "name") String name,HttpServletRequest httpServletRequest) throws Exception {
        return passwordsService.validateEmail(email,name,httpServletRequest);
    }

    @PostMapping("/verify-otp/{email}/{otp}")
    public ResponseEntity<String> verifyOtp(@PathVariable("otp")int otp,@PathVariable("email")String  email) throws Exception {
        return passwordsService.verifyOtp(email,otp);
    }


    @PostMapping("/reset-password/{new-password}/{email}")
    public ResponseEntity<String> changePassword(@PathVariable(name = "new-password")  String newPassword,@PathVariable String email) throws Exception {
        return passwordsService.changePassword(newPassword,email);
    }

    @GetMapping("/healthCheck")
    public ResponseEntity<String> healthCheck(){
        return ResponseEntity.ok("Success");
    }

}