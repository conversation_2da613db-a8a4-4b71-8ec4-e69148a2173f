package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
        @Table(name = "status_master")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StatusMaster extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "statusmastersequencegenerator")
    @GenericGenerator(
            name = "statusmastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "statusmastersequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "status_name", nullable = false)
    private String statusName;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "status_for_id", nullable = false)
    private StatusForMaster statusForId;

}
