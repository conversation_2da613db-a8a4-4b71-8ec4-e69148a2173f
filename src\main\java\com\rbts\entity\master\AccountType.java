package com.rbts.entity.master;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "account_type")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountType {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "accountTypeSeqGen")
    @SequenceGenerator(name = "accountTypeSeqGen", sequenceName = "account_type_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "account_type_key", nullable = false, unique = true)
    private String accountTypeKey;

    @Column(name = "account_type_desc")
    private String accountTypeDesc;
}
