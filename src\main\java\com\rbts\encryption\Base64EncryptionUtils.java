package com.rbts.encryption;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;


@Data
@AllArgsConstructor
@Component
public class Base64EncryptionUtils {

    private static final String SECRET_KEY = "VStateFilingsJwt";

    private static final String ENCRYPTION_PREFIX = "ENC:"; // Prefix to identify encrypted values


    // Encrypts a string and encodes it using Base64
    public String encrypt(String data ) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return ENCRYPTION_PREFIX +Base64.getEncoder().encodeToString(encryptedBytes);
    }


    public String decrypt(String encryptedData) throws Exception {
        if (encryptedData == null || encryptedData.isEmpty()) {
            return encryptedData; // Return as-is if the input is null or empty.
        }

        try {
            byte[] keyBytes = SECRET_KEY.getBytes(StandardCharsets.UTF_8);
            SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // Attempt to decode and decrypt
            byte[] decodedValue = Base64.getDecoder().decode(encryptedData.substring(ENCRYPTION_PREFIX.length()));
            byte[] decValue = cipher.doFinal(decodedValue);
            return new String(decValue, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // If decryption fails, return the original string
            return encryptedData;
        }
    }



}
