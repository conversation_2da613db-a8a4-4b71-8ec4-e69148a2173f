package com.rbts.entity;


import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "query_for_master")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryForMaster extends BaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "queryformastersequencegenerator")
    @GenericGenerator(
            name = "queryformastersequencegenerator",
            strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence_name", value = "queryformastersequencegenerator"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "1000"), // Set the starting value here
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
            }
    )
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "query_for_name",nullable = false, unique = true,length = 25)
    private String queryForName;

    @NotNull
    @Column(name = "query_for_desc",nullable = false)
    private String queryForDesc;

}
